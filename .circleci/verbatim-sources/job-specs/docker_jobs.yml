  docker_build_job:
      parameters:
        image_name:
          type: string
          default: ""
      machine:
        image: ubuntu-2004:202104-01
      resource_class: large
      environment:
        IMAGE_NAME: << parameters.image_name >>
        # Enable 'docker manifest'
        DOCKER_CLI_EXPERIMENTAL: "enabled"
        DOCKER_BUILDKIT: 1
      steps:
        - checkout
        - calculate_docker_image_tag
        - run:
            name: Check if image should be built
            command: |
              set +x
              export AWS_ACCESS_KEY_ID=${CIRCLECI_AWS_ACCESS_KEY_FOR_DOCKER_BUILDER_V1}
              export AWS_SECRET_ACCESS_KEY=${CIRCLECI_AWS_SECRET_KEY_FOR_DOCKER_BUILDER_V1}
              export AWS_ACCOUNT_ID=$(aws sts get-caller-identity|grep Account|cut -f4 -d\")
              export AWS_REGION=us-east-1
              aws ecr get-login-password --region $AWS_REGION|docker login --username AWS \
                       --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com
              set -x
              # Check if image already exists, if it does then skip building it
              if docker manifest inspect "************.dkr.ecr.us-east-1.amazonaws.com/pytorch/${IMAGE_NAME}:${DOCKER_TAG}"; then
                circleci-agent step halt
                # circleci-agent step halt doesn't actually halt the step so we need to
                # explicitly exit the step here ourselves before it causes too much trouble
                exit 0
              fi
              # Covers the case where a previous tag doesn't exist for the tree
              # this is only really applicable on trees that don't have `.ci/docker` at its merge base, i.e. nightly
              if ! git rev-parse "$(git merge-base HEAD << pipeline.git.base_revision >>):.ci/docker"; then
                echo "Directory '.ci/docker' not found in tree << pipeline.git.base_revision >>, you should probably rebase onto a more recent commit"
                exit 1
              fi
              PREVIOUS_DOCKER_TAG=$(git rev-parse "$(git merge-base HEAD << pipeline.git.base_revision >>):ci/docker")
              # If no image exists but the hash is the same as the previous hash then we should error out here
              if [[ "${PREVIOUS_DOCKER_TAG}" = "${DOCKER_TAG}" ]]; then
                echo "ERROR: Something has gone wrong and the previous image isn't available for the merge-base of your branch"
                echo "       contact the PyTorch team to restore the original images"
                exit 1
              fi
        - run:
            name: build_docker_image_<< parameters.image_name >>
            no_output_timeout: "1h"
            command: |
              set +x
              export AWS_ACCESS_KEY_ID=${CIRCLECI_AWS_ACCESS_KEY_FOR_DOCKER_BUILDER_V1}
              export AWS_SECRET_ACCESS_KEY=${CIRCLECI_AWS_SECRET_KEY_FOR_DOCKER_BUILDER_V1}
              set -x
              cd .ci/docker && ./build_docker.sh
