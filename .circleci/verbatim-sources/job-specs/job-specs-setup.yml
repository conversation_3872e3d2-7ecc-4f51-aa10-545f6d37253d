
  setup:
    docker:
      - image: circleci/python:3.7.3
    steps:
      - checkout
      - run:
          name: Save commit message
          command: git log --format='%B' -n 1 HEAD > .circleci/scripts/COMMIT_MSG
      # Note [Workspace for CircleCI scripts]
      # ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      # In the beginning, you wrote your CI scripts in a
      # .circleci/config.yml file, and life was good.  Your CI
      # configurations flourished and multiplied.
      #
      # Then one day, CircleCI cometh down high and say, "Your YAML file
      # is too biggeth, it stresses our servers so."  And thus they
      # asketh us to smite the scripts in the yml file.
      #
      # But you can't just put the scripts in the .circleci folder,
      # because in some jobs, you don't ever actually checkout the
      # source repository.  Where you gonna get the scripts from?
      #
      # Here's how you do it: you persist .circleci/scripts into a
      # workspace, attach the workspace in your subjobs, and run all
      # your scripts from there.
      - persist_to_workspace:
          root: .
          paths: .circleci/scripts
