
  # update_s3_htmls job
  # These jobs create html files for every cpu/cu## folder in s3. The html
  # files just store the names of all the files in that folder (which are
  # binary files (.whl files)). This is to allow pip installs of the latest
  # version in a folder without having to know the latest date. <PERSON><PERSON> has a flag
  # -f that you can pass an html file listing a bunch of packages, and pip will
  # then install the one with the most recent version.
  update_s3_htmls: &update_s3_htmls
    machine:
      image: ubuntu-2004:202104-01
    resource_class: medium
    steps:
    - checkout
    - setup_linux_system_environment
    - run:
        <<: *binary_checkout
    # N.B. we do not run binary_populate_env. The only variable we need is
    # PIP_UPLOAD_FOLDER (which is 'nightly/' for the nightlies and '' for
    # releases, and sometimes other things for special cases). Instead we
    # expect PIP_UPLOAD_FOLDER to be passed directly in the env. This is
    # because, unlike all the other binary jobs, these jobs only get run once,
    # in a separate workflow. They are not a step in other binary jobs like
    # build, test, upload.
    #
    # You could attach this to every job, or include it in the upload step if
    # you wanted. You would need to add binary_populate_env in this case to
    # make sure it has the same upload folder as the job it's attached to. This
    # function is idempotent, so it won't hurt anything; it's just a little
    # unnescessary"
    - run:
        name: define PIP_UPLOAD_FOLDER
        command: |
          our_upload_folder=nightly/
          # On tags upload to test instead
          if [[ -n "${CIRCLE_TAG}" ]]; then
            our_upload_folder=test/
          fi
          echo "export PIP_UPLOAD_FOLDER=${our_upload_folder}" >> ${BASH_ENV}
    - run:
        name: Update s3 htmls
        no_output_timeout: "1h"
        command: |
          set +x
          echo "declare -x \"AWS_ACCESS_KEY_ID=${PYTORCH_BINARY_AWS_ACCESS_KEY_ID}\"" >> /home/<USER>/project/env
          echo "declare -x \"AWS_SECRET_ACCESS_KEY=${PYTORCH_BINARY_AWS_SECRET_ACCESS_KEY}\"" >> /home/<USER>/project/env
          source /home/<USER>/project/env
          set -eux -o pipefail
          retry () {
              $*  || (sleep 1 && $*) || (sleep 2 && $*) || (sleep 4 && $*) || (sleep 8 && $*)
          }
          retry pip install awscli==1.6
          "/home/<USER>/project/builder/cron/update_s3_htmls.sh"
