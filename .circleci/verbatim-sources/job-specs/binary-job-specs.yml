jobs:
  binary_ios_build:
    <<: *pytorch_ios_params
    macos:
      xcode: "12.5.1"
    steps:
    - attach_workspace:
        at: ~/workspace
    - checkout
    - run_brew_for_ios_build
    - run:
        name: Build
        no_output_timeout: "1h"
        command: |
          script="/Users/<USER>/project/.circleci/scripts/binary_ios_build.sh"
          cat "$script"
          source "$script"
    - run:
        name: Test
        no_output_timeout: "30m"
        command: |
          script="/Users/<USER>/project/.circleci/scripts/binary_ios_test.sh"
          cat "$script"
          source "$script"
    - persist_to_workspace:
        root: /Users/<USER>/workspace/
        paths: ios

  binary_ios_upload:
    <<: *pytorch_ios_params
    macos:
      xcode: "12.5.1"
    steps:
    - attach_workspace:
        at: ~/workspace
    - checkout
    - run_brew_for_ios_build
    - run:
        name: Upload
        no_output_timeout: "1h"
        command: |
          script="/Users/<USER>/project/.circleci/scripts/binary_ios_upload.sh"
          cat "$script"
          source "$script"

  anaconda_prune:
    parameters:
      packages:
        type: string
        description: "What packages are we pruning? (quoted, space-separated string. eg. 'pytorch', 'torchvision torchaudio', etc.)"
        default: "pytorch"
      channel:
        type: string
        description: "What channel are we pruning? (eq. pytorch-nightly)"
        default: "pytorch-nightly"
    docker:
      - image: continuumio/miniconda3
    environment:
      - PACKAGES: "<< parameters.packages >>"
      - CHANNEL: "<< parameters.channel >>"
    steps:
      - checkout
      - run:
          name: Install dependencies
          no_output_timeout: "1h"
          command: |
            conda install -yq anaconda-client
      - run:
          name: Prune packages
          no_output_timeout: "1h"
          command: |
              ANACONDA_API_TOKEN="${CONDA_PYTORCHBOT_TOKEN}" \
              scripts/release/anaconda-prune/run.sh
