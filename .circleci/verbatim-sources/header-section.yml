# WARNING: DO NOT EDIT THIS FILE DIRECTLY!!!
# See the README.md in this directory.

# IMPORTANT: To update Docker image version, please follow
# the instructions at
# https://github.com/pytorch/pytorch/wiki/Docker-image-build-on-CircleCI

version: 2.1

parameters:
  run_binary_tests:
    type: boolean
    default: false
  run_build:
    type: boolean
    default: true
  run_master_build:
    type: boolean
    default: false
  run_slow_gradcheck_build:
    type: boolean
    default: false

executors:
  windows-with-nvidia-gpu:
    machine:
      resource_class: windows.gpu.nvidia.medium
      image: windows-server-2019-nvidia:previous
      shell: bash.exe

  windows-xlarge-cpu-with-nvidia-cuda:
    machine:
      resource_class: windows.xlarge
      image: windows-server-2019-vs2019:stable
      shell: bash.exe

  windows-medium-cpu-with-nvidia-cuda:
    machine:
      resource_class: windows.medium
      image: windows-server-2019-vs2019:stable
      shell: bash.exe
