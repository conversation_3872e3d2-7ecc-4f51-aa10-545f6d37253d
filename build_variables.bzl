# WARNING: the contents of this file must BOTH be valid Starlark (for <PERSON> and

# <PERSON><PERSON>) as well as valid Python (for our cmake build).  This means that
# load() directives are not allowed (as they are not recognized by Python).
# If you want to fix this, figure out how run this file from cmake with a proper
# Starlark interpreter as part of the default OSS build process.  If you need
# some nontrivial Starlark features, make a separate bzl file (remember that

# bzl files are not exported via ShipIt by default, so you may also need to
# update PyTorch's ShipIt config)

# This is duplicated in caffe2/CMakeLists.txt for now and not yet used in buck
GENERATED_LAZY_TS_CPP = [
    "lazy/generated/LazyNativeFunctions.cpp",
    "lazy/generated/RegisterAutogradLazy.cpp",
    "lazy/generated/RegisterLazy.cpp",
]

# NVFuser runtime library
libtorch_nvfuser_runtime_sources = [
    "third_party/nvfuser/runtime/array.cu",
    "third_party/nvfuser/runtime/array_rocm.cu",
    "third_party/nvfuser/runtime/bf16_support.cu",
    "third_party/nvfuser/runtime/bf16_support_rocm.cu",
    "third_party/nvfuser/runtime/block_reduction.cu",
    "third_party/nvfuser/runtime/block_sync_atomic.cu",
    "third_party/nvfuser/runtime/block_sync_default.cu",
    "third_party/nvfuser/runtime/block_sync_default_rocm.cu",
    "third_party/nvfuser/runtime/broadcast.cu",
    "third_party/nvfuser/runtime/fp16_support.cu",
    "third_party/nvfuser/runtime/fused_reduction.cu",
    "third_party/nvfuser/runtime/fused_welford_helper.cu",
    "third_party/nvfuser/runtime/fused_welford_impl.cu",
    "third_party/nvfuser/runtime/grid_broadcast.cu",
    "third_party/nvfuser/runtime/grid_reduction.cu",
    "third_party/nvfuser/runtime/grid_sync.cu",
    "third_party/nvfuser/runtime/helpers.cu",
    "third_party/nvfuser/runtime/index_utils.cu",
    "third_party/nvfuser/runtime/memory.cu",
    "third_party/nvfuser/runtime/random_numbers.cu",
    "third_party/nvfuser/runtime/swizzle.cu",
    "third_party/nvfuser/runtime/tensor.cu",
    "third_party/nvfuser/runtime/tensorcore.cu",
    "third_party/nvfuser/runtime/tuple.cu",
    "third_party/nvfuser/runtime/type_traits.cu",
    "third_party/nvfuser/runtime/warp.cu",
    "third_party/nvfuser/runtime/warp_rocm.cu",
    "third_party/nvfuser/runtime/welford.cu",
    "aten/src/ATen/cuda/detail/PhiloxCudaStateRaw.cuh",
    "aten/src/ATen/cuda/detail/UnpackRaw.cuh",
]

libtorch_nvfuser_generated_headers = ["{}.h".format(name.split("/")[-1].split(".")[0]) for name in libtorch_nvfuser_runtime_sources]

def libtorch_generated_sources(gencode_pattern):
    return [gencode_pattern.format(name) for name in [
        "torch/csrc/autograd/generated/Functions.cpp",
        "torch/csrc/autograd/generated/VariableType_0.cpp",
        "torch/csrc/autograd/generated/VariableType_1.cpp",
        "torch/csrc/autograd/generated/VariableType_2.cpp",
        "torch/csrc/autograd/generated/VariableType_3.cpp",
        "torch/csrc/autograd/generated/VariableType_4.cpp",
        "torch/csrc/autograd/generated/TraceType_0.cpp",
        "torch/csrc/autograd/generated/TraceType_1.cpp",
        "torch/csrc/autograd/generated/TraceType_2.cpp",
        "torch/csrc/autograd/generated/TraceType_3.cpp",
        "torch/csrc/autograd/generated/TraceType_4.cpp",
        "torch/csrc/autograd/generated/ADInplaceOrViewType_0.cpp",
        "torch/csrc/autograd/generated/ADInplaceOrViewType_1.cpp",
    ]]

# copied from https://github.com/pytorch/pytorch/blob/f99a693cd9ff7a9b5fdc71357dac66b8192786d3/aten/src/ATen/core/CMakeLists.txt
jit_core_headers = [
    "torch/csrc/utils/memory.h",
    "torch/csrc/Export.h",
    "torch/csrc/jit/frontend/source_range.h",
    "torch/csrc/jit/serialization/callstack_debug_info_serialization.h",
    "torch/csrc/jit/serialization/source_range_serialization.h",
    "torch/csrc/jit/frontend/lexer.h",
    "torch/csrc/jit/frontend/strtod.h",
    "torch/csrc/jit/frontend/parser_constants.h",
    "torch/csrc/jit/frontend/function_schema_parser.h",
    "torch/csrc/jit/frontend/parse_string_literal.h",
    "torch/csrc/jit/frontend/schema_type_parser.h",
    "torch/csrc/jit/frontend/error_report.h",
    "torch/csrc/jit/frontend/tree.h",
    "torch/custom_class.h",
    "torch/custom_class_detail.h",
    "torch/library.h",
]

jit_core_sources = [
    "torch/csrc/jit/frontend/error_report.cpp",
    "torch/csrc/jit/frontend/function_schema_parser.cpp",
    "torch/csrc/jit/frontend/lexer.cpp",
    "torch/csrc/jit/frontend/schema_type_parser.cpp",
    "torch/csrc/jit/frontend/strtod.cpp",
    "torch/csrc/jit/frontend/source_range.cpp",
]

# copied from https://github.com/pytorch/pytorch/blob/0bde610c14b92d351b968a0228df29e92442b1cc/torch/CMakeLists.txt
# There are some common files used in both internal lite-interpreter and full-jit. Making a separate
# list for the shared files.

core_sources_common = [
    "torch/csrc/autograd/autograd_meta.cpp",
    "torch/csrc/autograd/forward_grad.cpp",
    "torch/csrc/jit/frontend/edit_distance.cpp",
    "torch/csrc/jit/mobile/compatibility/runtime_compatibility.cpp",
    "torch/csrc/jit/mobile/type_parser.cpp",
    "torch/csrc/jit/operator_upgraders/version_map.cpp",
    "torch/csrc/jit/runtime/instruction.cpp",
    "torch/csrc/jit/runtime/jit_exception.cpp",
    "torch/csrc/jit/runtime/operator.cpp",
    "torch/csrc/jit/mobile/register_ops_common_utils.cpp",
    "torch/csrc/jit/runtime/print_handler.cpp",
    "torch/csrc/jit/runtime/slice_indices_adjust.cpp",
    "torch/csrc/jit/runtime/register_ops_utils.cpp",
    "torch/csrc/jit/runtime/vararg_functions.cpp",
    "torch/csrc/jit/mobile/promoted_prim_ops.cpp",
    "torch/csrc/jit/mobile/prim_ops_registery.cpp",
    "torch/csrc/profiler/util.cpp",
]

torch_unpickler_common = [
    "torch/csrc/jit/serialization/import_read.cpp",
    "torch/csrc/jit/serialization/unpickler.cpp",
]

libtorch_sources_common = sorted(core_sources_common + torch_unpickler_common)

# The profilers are not needed in the lite interpreter build.
libtorch_profiler_sources = [
    "torch/csrc/autograd/profiler_legacy.cpp",
    "torch/csrc/autograd/profiler_kineto.cpp",
    "torch/csrc/profiler/collection.cpp",
    "torch/csrc/profiler/data_flow.cpp",
    "torch/csrc/profiler/kineto_shim.cpp",
    "torch/csrc/profiler/kineto_client_interface.cpp",
    "torch/csrc/profiler/orchestration/observer.cpp",
    "torch/csrc/profiler/orchestration/python_tracer.cpp",
    "torch/csrc/profiler/standalone/execution_graph_observer.cpp",
    "torch/csrc/profiler/standalone/itt_observer.cpp",
    "torch/csrc/profiler/standalone/nvtx_observer.cpp",
    "torch/csrc/profiler/stubs/base.cpp",
    "torch/csrc/profiler/orchestration/vulkan.cpp",
    "torch/csrc/profiler/perf.cpp",
    "torch/csrc/monitor/counters.cpp",
    "torch/csrc/monitor/events.cpp",
]

libtorch_edge_profiler_sources = libtorch_profiler_sources + [
    "torch/csrc/jit/mobile/profiler_edge.cpp",
]

core_trainer_sources = [
    "torch/csrc/autograd/anomaly_mode.cpp",
    "torch/csrc/autograd/autograd.cpp",
    "torch/csrc/autograd/autograd_not_implemented_fallback.cpp",
    "torch/csrc/autograd/cpp_hook.cpp",
    "torch/csrc/autograd/custom_function.cpp",
    "torch/csrc/autograd/engine.cpp",
    "torch/csrc/autograd/function.cpp",
    "torch/csrc/autograd/functions/accumulate_grad.cpp",
    "torch/csrc/autograd/functions/basic_ops.cpp",
    "torch/csrc/autograd/functions/tensor.cpp",
    "torch/csrc/autograd/functions/utils.cpp",
    "torch/csrc/autograd/input_buffer.cpp",
    "torch/csrc/autograd/record_function_ops.cpp",
    "torch/csrc/autograd/saved_variable.cpp",
    "torch/csrc/autograd/variable.cpp",
    "torch/csrc/autograd/utils/warnings.cpp",
    "torch/csrc/autograd/jit_decomp_interface.cpp",
    "torch/csrc/jit/frontend/name_mangler.cpp",
    "torch/csrc/jit/ir/type_hashing.cpp",
    "torch/csrc/jit/serialization/pickler.cpp",
    "torch/csrc/jit/serialization/type_name_uniquer.cpp",
]

torch_mobile_core = [
    # backend_debug_info.cpp provides
    # __torch__.torch.classes.backend.BackendDebugInfo class
    # This should not be needed eventually.
    # TODO: Remove this dependency
    "torch/csrc/jit/backends/backend_debug_info.cpp",
    "torch/csrc/jit/mobile/compatibility/model_compatibility.cpp",
    "torch/csrc/jit/mobile/function.cpp",
    "torch/csrc/jit/mobile/import.cpp",
    "torch/csrc/jit/mobile/flatbuffer_loader.cpp",
    "torch/csrc/jit/mobile/interpreter.cpp",
    "torch/csrc/jit/mobile/module.cpp",
    "torch/csrc/jit/mobile/observer.cpp",
    "torch/csrc/jit/mobile/parse_bytecode.cpp",
    "torch/csrc/jit/mobile/parse_operators.cpp",
    "torch/csrc/jit/mobile/quantization.cpp",
    "torch/csrc/jit/mobile/upgrader_mobile.cpp",
    "torch/csrc/jit/runtime/register_prim_ops.cpp",
    "torch/csrc/jit/runtime/register_special_ops.cpp",
]

core_sources_full_mobile_no_backend_interface_xplat = [
    "torch/csrc/jit/api/function_impl.cpp",
    "torch/csrc/jit/api/module.cpp",
    "torch/csrc/jit/api/object.cpp",
    "torch/csrc/jit/backends/backend_debug_handler.cpp",
    "torch/csrc/jit/backends/backend_detail.cpp",
    "torch/csrc/jit/backends/backend_resolver.cpp",
    "torch/csrc/jit/codegen/fuser/codegen.cpp",
    "torch/csrc/jit/codegen/fuser/compiler.cpp",
    "torch/csrc/jit/codegen/fuser/executor.cpp",
    "torch/csrc/jit/codegen/fuser/fallback.cpp",
    "torch/csrc/jit/codegen/fuser/interface.cpp",
    "torch/csrc/jit/codegen/fuser/kernel_cache.cpp",
    "torch/csrc/jit/frontend/builtin_functions.cpp",
    "torch/csrc/jit/frontend/versioned_symbols.cpp",
    "torch/csrc/jit/frontend/canonicalize_modified_loop.cpp",
    "torch/csrc/jit/frontend/convert_to_ssa.cpp",
    "torch/csrc/jit/frontend/exit_transforms.cpp",
    "torch/csrc/jit/frontend/inline_loop_condition.cpp",
    "torch/csrc/jit/frontend/ir_emitter.cpp",
    "torch/csrc/jit/frontend/parser.cpp",
    "torch/csrc/jit/frontend/schema_matching.cpp",
    "torch/csrc/jit/frontend/script_type_parser.cpp",
    "torch/csrc/jit/frontend/sugared_value.cpp",
    "torch/csrc/jit/frontend/tracer.cpp",
    "torch/csrc/jit/ir/alias_analysis.cpp",
    "torch/csrc/jit/ir/attributes.cpp",
    "torch/csrc/jit/ir/constants.cpp",
    "torch/csrc/jit/ir/ir.cpp",
    "torch/csrc/jit/ir/irparser.cpp",
    "torch/csrc/jit/ir/node_hashing.cpp",
    "torch/csrc/jit/ir/scope.cpp",
    "torch/csrc/jit/ir/subgraph_matcher.cpp",
    "torch/csrc/jit/ir/graph_utils.cpp",
    "torch/csrc/jit/jit_log.cpp",
    "torch/csrc/jit/jit_opt_limit.cpp",
    "torch/csrc/jit/mobile/nnc/aot_compiler.cpp",
    "torch/csrc/jit/mobile/nnc/backend.cpp",
    "torch/csrc/jit/mobile/nnc/context.cpp",
    "torch/csrc/jit/mobile/nnc/registry.cpp",
    "torch/csrc/jit/operator_upgraders/utils.cpp",
    "torch/csrc/jit/operator_upgraders/upgraders.cpp",
    "torch/csrc/jit/operator_upgraders/upgraders_entry.cpp",
    "torch/csrc/jit/passes/add_if_then_else.cpp",
    "torch/csrc/jit/passes/annotate_warns.cpp",
    "torch/csrc/jit/passes/bailout_graph.cpp",
    "torch/csrc/jit/passes/check_strict_fusion.cpp",
    "torch/csrc/jit/passes/batch_mm.cpp",
    "torch/csrc/jit/passes/canonicalize.cpp",
    "torch/csrc/jit/passes/canonicalize_graph_fuser_ops.cpp",
    "torch/csrc/jit/passes/clear_profiling.cpp",
    "torch/csrc/jit/passes/clear_undefinedness.cpp",
    "torch/csrc/jit/passes/common_subexpression_elimination.cpp",
    "torch/csrc/jit/passes/concat_opt.cpp",
    "torch/csrc/jit/passes/constant_pooling.cpp",
    "torch/csrc/jit/passes/constant_propagation.cpp",
    "torch/csrc/jit/passes/restore_mutation.cpp",
    "torch/csrc/jit/passes/create_autodiff_subgraphs.cpp",
    "torch/csrc/jit/passes/cuda_graph_fuser.cpp",
    "torch/csrc/jit/passes/dead_code_elimination.cpp",
    "torch/csrc/jit/passes/eliminate_no_ops.cpp",
    "torch/csrc/jit/passes/remove_redundant_profiles.cpp",
    "torch/csrc/jit/passes/remove_exceptions.cpp",
    "torch/csrc/jit/passes/decompose_ops.cpp",
    "torch/csrc/jit/passes/dtype_analysis.cpp",
    "torch/csrc/jit/passes/device_type_analysis.cpp",
    "torch/csrc/jit/passes/erase_number_types.cpp",
    "torch/csrc/jit/passes/fixup_trace_scope_blocks.cpp",
    "torch/csrc/jit/passes/freeze_module.cpp",
    "torch/csrc/jit/passes/fuse_linear.cpp",
    "torch/csrc/jit/passes/fuse_relu.cpp",
    "torch/csrc/jit/passes/graph_fuser.cpp",
    "torch/csrc/jit/passes/graph_rewrite_helper.cpp",
    "torch/csrc/jit/passes/guard_elimination.cpp",
    "torch/csrc/jit/passes/hoist_conv_packed_params.cpp",
    "torch/csrc/jit/passes/inline_autodiff_subgraphs.cpp",
    "torch/csrc/jit/passes/inline_forked_closures.cpp",
    "torch/csrc/jit/passes/inline_fork_wait.cpp",
    "torch/csrc/jit/passes/inliner.cpp",
    "torch/csrc/jit/passes/inplace_check.cpp",
    "torch/csrc/jit/passes/insert_guards.cpp",
    "torch/csrc/jit/passes/lift_closures.cpp",
    "torch/csrc/jit/passes/liveness.cpp",
    "torch/csrc/jit/passes/loop_unrolling.cpp",
    "torch/csrc/jit/passes/lower_grad_of.cpp",
    "torch/csrc/jit/passes/lower_tuples.cpp",
    "torch/csrc/jit/passes/normalize_ops.cpp",
    "torch/csrc/jit/passes/peephole_dict_idioms.cpp",
    "torch/csrc/jit/passes/peephole_list_idioms.cpp",
    "torch/csrc/jit/passes/value_refinement_utils.cpp",
    "torch/csrc/jit/passes/peephole_alias_sensitive.cpp",
    "torch/csrc/jit/passes/pass_manager.cpp",
    "torch/csrc/jit/passes/peephole.cpp",
    "torch/csrc/jit/passes/peephole_non_tensor.cpp",
    "torch/csrc/jit/passes/create_functional_graphs.cpp",
    "torch/csrc/jit/passes/refine_tuple_types.cpp",
    "torch/csrc/jit/passes/remove_mutation.cpp",
    "torch/csrc/jit/passes/prepack_folding.cpp",
    "torch/csrc/jit/passes/fold_conv_bn.cpp",
    "torch/csrc/jit/passes/fold_linear_bn.cpp",
    "torch/csrc/jit/passes/dbr_quantization/remove_redundant_aliases.cpp",
    "torch/csrc/jit/passes/frozen_concat_linear.cpp",
    "torch/csrc/jit/passes/frozen_conv_add_relu_fusion.cpp",
    "torch/csrc/jit/passes/frozen_conv_folding.cpp",
    "torch/csrc/jit/passes/frozen_linear_folding.cpp",
    "torch/csrc/jit/passes/frozen_linear_transpose.cpp",
    "torch/csrc/jit/passes/frozen_ops_to_mkldnn.cpp",
    "torch/csrc/jit/passes/frozen_graph_optimizations.cpp",
    "torch/csrc/jit/passes/remove_expands.cpp",
    "torch/csrc/jit/passes/remove_dropout.cpp",
    "torch/csrc/jit/passes/requires_grad_analysis.cpp",
    "torch/csrc/jit/passes/shape_analysis.cpp",
    "torch/csrc/jit/passes/integer_value_refinement.cpp",
    "torch/csrc/jit/passes/replacement_of_old_operators.cpp",
    "torch/csrc/jit/passes/symbolic_shape_analysis.cpp",
    "torch/csrc/jit/passes/symbolic_shape_cache.cpp",
    "torch/csrc/jit/passes/symbolic_shape_runtime_fusion.cpp",
    "torch/csrc/jit/passes/specialize_autogradzero.cpp",
    "torch/csrc/jit/passes/update_differentiable_graph_requires_grad.cpp",
    "torch/csrc/jit/passes/variadic_ops.cpp",
    "torch/csrc/jit/passes/subgraph_rewrite.cpp",
    "torch/csrc/jit/passes/tensorexpr_fuser.cpp",
    "torch/csrc/jit/passes/utils/memory_dag.cpp",
    "torch/csrc/jit/passes/utils/subgraph_utils.cpp",
    "torch/csrc/jit/passes/utils/optimization_utils.cpp",
    "torch/csrc/jit/passes/utils/op_registry.cpp",
    "torch/csrc/jit/passes/mkldnn_rewrite.cpp",
    "torch/csrc/jit/passes/xnnpack_rewrite.cpp",
    "torch/csrc/jit/passes/vulkan_rewrite.cpp",
    "torch/csrc/jit/passes/metal_rewrite.cpp",
    "torch/csrc/jit/passes/quantization/helper.cpp",
    "torch/csrc/jit/passes/quantization/quantization_type.cpp",
    "torch/csrc/jit/passes/quantization/insert_observers.cpp",
    "torch/csrc/jit/passes/quantization/insert_quant_dequant.cpp",
    "torch/csrc/jit/passes/quantization/dedup_module_uses.cpp",
    "torch/csrc/jit/passes/quantization/finalize.cpp",
    "torch/csrc/jit/passes/quantization/fusion_passes.cpp",
    "torch/csrc/jit/passes/quantization/register_packed_params.cpp",
    "torch/csrc/jit/python/update_graph_executor_opt.cpp",
    "torch/csrc/jit/runtime/argument_spec.cpp",
    "torch/csrc/jit/runtime/autodiff.cpp",
    "torch/csrc/jit/runtime/graph_executor.cpp",
    "torch/csrc/jit/runtime/interpreter/frame.cpp",
    "torch/csrc/jit/runtime/interpreter/preprocess_graph.cpp",
    "torch/csrc/jit/runtime/interpreter.cpp",
    "torch/csrc/jit/runtime/logging.cpp",
    "torch/csrc/jit/runtime/simple_graph_executor_impl.cpp",
    "torch/csrc/jit/runtime/profiling_graph_executor_impl.cpp",
    "torch/csrc/jit/runtime/profiling_record.cpp",
    "torch/csrc/jit/runtime/script_profile.cpp",
    "torch/csrc/jit/runtime/symbolic_script.cpp",
    "torch/csrc/jit/runtime/symbolic_shape_registry.cpp",
    "torch/csrc/jit/runtime/decomposition_registry.cpp",
    "torch/csrc/jit/runtime/decomposition_registry_util.cpp",
    "torch/csrc/jit/runtime/serialized_shape_function_registry.cpp",
    "torch/csrc/jit/runtime/symbolic_shape_registry_util.cpp",
    "torch/csrc/jit/runtime/jit_trace.cpp",
    "torch/csrc/jit/serialization/callstack_debug_info_serialization.cpp",
    "torch/csrc/jit/serialization/import.cpp",
    "torch/csrc/jit/serialization/import_export_helpers.cpp",
    "torch/csrc/jit/serialization/import_source.cpp",
    "torch/csrc/jit/serialization/pickle.cpp",
    "torch/csrc/jit/serialization/python_print.cpp",
    "torch/csrc/jit/serialization/source_range_serialization.cpp",
    "torch/csrc/jit/tensorexpr/block_codegen.cpp",
    "torch/csrc/jit/tensorexpr/bounds_inference.cpp",
    "torch/csrc/jit/tensorexpr/bounds_overlap.cpp",
    "torch/csrc/jit/tensorexpr/codegen.cpp",
    "torch/csrc/jit/tensorexpr/cpp_codegen.cpp",
    "torch/csrc/jit/tensorexpr/eval.cpp",
    "torch/csrc/jit/tensorexpr/expr.cpp",
    "torch/csrc/jit/tensorexpr/external_functions_core.cpp",
    "torch/csrc/jit/tensorexpr/external_functions_registry.cpp",
    "torch/csrc/jit/tensorexpr/graph_opt.cpp",
    "torch/csrc/jit/tensorexpr/hash_provider.cpp",
    "torch/csrc/jit/tensorexpr/intrinsic_symbols.cpp",
    "torch/csrc/jit/tensorexpr/ir.cpp",
    "torch/csrc/jit/tensorexpr/ir_cloner.cpp",
    "torch/csrc/jit/tensorexpr/ir_mutator.cpp",
    "torch/csrc/jit/tensorexpr/ir_printer.cpp",
    "torch/csrc/jit/tensorexpr/ir_simplifier.cpp",
    "torch/csrc/jit/tensorexpr/ir_verifier.cpp",
    "torch/csrc/jit/tensorexpr/ir_visitor.cpp",
    "torch/csrc/jit/tensorexpr/kernel.cpp",
    "torch/csrc/jit/tensorexpr/llvm_codegen.cpp",
    "torch/csrc/jit/tensorexpr/llvm_jit.cpp",
    "torch/csrc/jit/tensorexpr/loopnest.cpp",
    "torch/csrc/jit/tensorexpr/loopnest_randomization.cpp",
    "torch/csrc/jit/tensorexpr/lowerings.cpp",
    "torch/csrc/jit/tensorexpr/mem_dependency_checker.cpp",
    "torch/csrc/jit/tensorexpr/operators/conv2d.cpp",
    "torch/csrc/jit/tensorexpr/operators/matmul.cpp",
    "torch/csrc/jit/tensorexpr/operators/misc.cpp",
    "torch/csrc/jit/tensorexpr/operators/norm.cpp",
    "torch/csrc/jit/tensorexpr/operators/pointwise.cpp",
    "torch/csrc/jit/tensorexpr/operators/quantization.cpp",
    "torch/csrc/jit/tensorexpr/operators/reduction.cpp",
    "torch/csrc/jit/tensorexpr/operators/softmax.cpp",
    "torch/csrc/jit/tensorexpr/reduction.cpp",
    "torch/csrc/jit/tensorexpr/registerizer.cpp",
    "torch/csrc/jit/tensorexpr/tensor.cpp",
    "torch/csrc/jit/tensorexpr/types.cpp",
    "torch/csrc/jit/tensorexpr/unique_name_manager.cpp",
    "torch/csrc/jit/testing/file_check.cpp",
    "torch/csrc/jit/testing/hooks_for_testing.cpp",
    "torch/csrc/utils/cpp_stacktraces.cpp",
    "torch/csrc/utils/schema_info.cpp",
    "torch/csrc/utils/tensor_flatten.cpp",
    "torch/csrc/utils/variadic.cpp",
]

core_sources_full_mobile_no_backend_interface = core_sources_full_mobile_no_backend_interface_xplat + [
    # backend_debug_info.cpp provides
    # __torch__.torch.classes.backend.BackendDebugInfo class
    # This should not be needed eventually.
    # TODO: Remove this dependency
    "torch/csrc/jit/backends/backend_debug_info.cpp",
    "torch/csrc/jit/mobile/compatibility/model_compatibility.cpp",
    "torch/csrc/jit/mobile/function.cpp",
    "torch/csrc/jit/mobile/import.cpp",
    "torch/csrc/jit/mobile/flatbuffer_loader.cpp",
    "torch/csrc/jit/mobile/interpreter.cpp",
    "torch/csrc/jit/mobile/module.cpp",
    "torch/csrc/jit/mobile/observer.cpp",
    "torch/csrc/jit/mobile/parse_bytecode.cpp",
    "torch/csrc/jit/mobile/parse_operators.cpp",
    "torch/csrc/jit/mobile/quantization.cpp",
    "torch/csrc/jit/mobile/upgrader_mobile.cpp",
]


core_sources_full_mobile = core_sources_full_mobile_no_backend_interface + [
    "torch/csrc/jit/backends/backend_debug_info.cpp",
    "torch/csrc/jit/backends/backend_interface.cpp",
]

core_sources_full = core_sources_full_mobile + [
    "torch/csrc/jit/runtime/static/fusion.cpp",
    "torch/csrc/jit/runtime/static/generated_ops.cpp",
    "torch/csrc/jit/runtime/static/impl.cpp",
    "torch/csrc/jit/runtime/static/memory_planner.cpp",
    "torch/csrc/jit/runtime/static/native_ops.cpp",
    "torch/csrc/jit/runtime/static/ops.cpp",
    "torch/csrc/jit/runtime/static/passes.cpp",
    "torch/csrc/jit/runtime/static/te_wrapper.cpp",
    "torch/csrc/jit/tensorexpr/external_functions.cpp",
    "torch/csrc/jit/tensorexpr/external_functions_codegen.cpp",
]

lazy_tensor_core_sources = [
    "torch/csrc/lazy/backend/backend_device.cpp",
    "torch/csrc/lazy/backend/backend_interface.cpp",
    "torch/csrc/lazy/backend/lowering_context.cpp",
    "torch/csrc/lazy/core/config.cpp",
    "torch/csrc/lazy/core/debug_util.cpp",
    "torch/csrc/lazy/core/hash.cpp",
    "torch/csrc/lazy/core/helpers.cpp",
    "torch/csrc/lazy/core/ir.cpp",
    "torch/csrc/lazy/core/ir_dump_util.cpp",
    "torch/csrc/lazy/core/ir_metadata.cpp",
    "torch/csrc/lazy/core/ir_util.cpp",
    "torch/csrc/lazy/core/lazy_graph_executor.cpp",
    "torch/csrc/lazy/core/metrics.cpp",
    "torch/csrc/lazy/core/multi_wait.cpp",
    "torch/csrc/lazy/core/ops/arithmetic_ir_ops.cpp",
    "torch/csrc/lazy/core/ops/utils.cpp",
    "torch/csrc/lazy/core/permutation_util.cpp",
    "torch/csrc/lazy/core/shape.cpp",
    "torch/csrc/lazy/core/shape_inference.cpp",
    "torch/csrc/lazy/core/tensor.cpp",
    "torch/csrc/lazy/core/tensor_impl.cpp",
    "torch/csrc/lazy/core/tensor_util.cpp",
    "torch/csrc/lazy/core/thread_pool.cpp",
    "torch/csrc/lazy/core/trie.cpp",
]

# We can't build all of the ts backend under certain build configurations, e.g. mobile,
# since it depends on things like autograd, meta functions, which may be disabled
lazy_tensor_ts_sources = [
    "torch/csrc/lazy/ts_backend/dynamic_ir.cpp",
    "torch/csrc/lazy/ts_backend/config.cpp",
    "torch/csrc/lazy/ts_backend/ops/device_data.cpp",
    "torch/csrc/lazy/ts_backend/ops/random_ops.cpp",
    "torch/csrc/lazy/ts_backend/ops/generic.cpp",
    "torch/csrc/lazy/ts_backend/tensor_aten_ops.cpp",
    "torch/csrc/lazy/ts_backend/ts_autograd_functions.cpp",
    "torch/csrc/lazy/ts_backend/ts_backend_impl.cpp",
    "torch/csrc/lazy/ts_backend/ts_eager_fallback.cpp",
    "torch/csrc/lazy/ts_backend/ts_lowering_context.cpp",
    "torch/csrc/lazy/ts_backend/ts_native_functions.cpp",
    "torch/csrc/lazy/ts_backend/ts_node.cpp",
    "torch/csrc/lazy/ts_backend/ts_node_lowering.cpp",
]

lazy_tensor_core_python_sources = [
    "torch/csrc/lazy/python/init.cpp",
    "torch/csrc/lazy/python/python_util.cpp",
]

libtorch_core_sources = sorted(
    core_sources_common +
    torch_unpickler_common +
    core_sources_full +
    core_trainer_sources +
    libtorch_profiler_sources +
    lazy_tensor_core_sources,
)

# These files are the only ones that are supported on Windows.
libtorch_distributed_base_sources = [
    "torch/csrc/distributed/c10d/Backend.cpp",
    "torch/csrc/distributed/c10d/FileStore.cpp",
    "torch/csrc/distributed/c10d/GlooDeviceFactory.cpp",
    "torch/csrc/distributed/c10d/Ops.cpp",
    "torch/csrc/distributed/c10d/ParamCommsUtils.cpp",
    "torch/csrc/distributed/c10d/PrefixStore.cpp",
    "torch/csrc/distributed/c10d/ProcessGroup.cpp",
    "torch/csrc/distributed/c10d/ProcessGroupGloo.cpp",
    "torch/csrc/distributed/c10d/ProcessGroupMPI.cpp",
    "torch/csrc/distributed/c10d/ProcessGroupWrapper.cpp",
    "torch/csrc/distributed/c10d/Store.cpp",
    "torch/csrc/distributed/c10d/TCPStore.cpp",
    "torch/csrc/distributed/c10d/Utils.cpp",
    "torch/csrc/distributed/c10d/comm.cpp",
    "torch/csrc/distributed/c10d/debug.cpp",
    "torch/csrc/distributed/c10d/default_comm_hooks.cpp",
    "torch/csrc/distributed/c10d/exception.cpp",
    "torch/csrc/distributed/c10d/logger.cpp",
    "torch/csrc/distributed/c10d/logging.cpp",
    "torch/csrc/distributed/c10d/quantization/quantization.cpp",
    "torch/csrc/distributed/c10d/reducer.cpp",
    "torch/csrc/distributed/c10d/sequence_num.cpp",
    "torch/csrc/distributed/c10d/socket.cpp",
    "torch/csrc/distributed/c10d/Work.cpp",
]

# These files are only supported on Linux (and others) but not on Windows.
libtorch_distributed_extra_sources = [
    "torch/csrc/distributed/autograd/autograd.cpp",
    "torch/csrc/distributed/autograd/utils.cpp",
    "torch/csrc/distributed/autograd/context/container.cpp",
    "torch/csrc/distributed/autograd/context/context.cpp",
    "torch/csrc/distributed/autograd/engine/dist_engine.cpp",
    "torch/csrc/distributed/autograd/functions/recvrpc_backward.cpp",
    "torch/csrc/distributed/autograd/functions/sendrpc_backward.cpp",
    "torch/csrc/distributed/autograd/rpc_messages/autograd_metadata.cpp",
    "torch/csrc/distributed/autograd/rpc_messages/propagate_gradients_req.cpp",
    "torch/csrc/distributed/autograd/rpc_messages/propagate_gradients_resp.cpp",
    "torch/csrc/distributed/autograd/rpc_messages/cleanup_autograd_context_req.cpp",
    "torch/csrc/distributed/autograd/rpc_messages/cleanup_autograd_context_resp.cpp",
    "torch/csrc/distributed/autograd/rpc_messages/rpc_with_autograd.cpp",
    "torch/csrc/distributed/autograd/rpc_messages/rpc_with_profiling_req.cpp",
    "torch/csrc/distributed/autograd/rpc_messages/rpc_with_profiling_resp.cpp",
    "torch/csrc/distributed/autograd/rpc_messages/rref_backward_req.cpp",
    "torch/csrc/distributed/autograd/rpc_messages/rref_backward_resp.cpp",
    "torch/csrc/distributed/c10d/HashStore.cpp",
    "torch/csrc/distributed/c10d/ProcessGroupRoundRobin.cpp",
    "torch/csrc/distributed/rpc/agent_utils.cpp",
    "torch/csrc/distributed/rpc/message.cpp",
    "torch/csrc/distributed/rpc/profiler/remote_profiler_manager.cpp",
    "torch/csrc/distributed/rpc/profiler/server_process_global_profiler.cpp",
    "torch/csrc/distributed/rpc/python_call.cpp",
    "torch/csrc/distributed/rpc/python_remote_call.cpp",
    "torch/csrc/distributed/rpc/python_resp.cpp",
    "torch/csrc/distributed/rpc/request_callback.cpp",
    "torch/csrc/distributed/rpc/request_callback_no_python.cpp",
    "torch/csrc/distributed/rpc/rpc_agent.cpp",
    "torch/csrc/distributed/rpc/rref_context.cpp",
    "torch/csrc/distributed/rpc/rref_impl.cpp",
    "torch/csrc/distributed/rpc/rref_proto.cpp",
    "torch/csrc/distributed/rpc/script_call.cpp",
    "torch/csrc/distributed/rpc/script_remote_call.cpp",
    "torch/csrc/distributed/rpc/script_resp.cpp",
    "torch/csrc/distributed/rpc/tensorpipe_agent.cpp",
    "torch/csrc/distributed/rpc/tensorpipe_utils.cpp",
    "torch/csrc/distributed/rpc/testing/faulty_tensorpipe_agent.cpp",
    "torch/csrc/distributed/rpc/torchscript_functions.cpp",
    "torch/csrc/distributed/rpc/types.cpp",
    "torch/csrc/distributed/rpc/utils.cpp",
]

libtorch_distributed_sources = libtorch_distributed_base_sources + libtorch_distributed_extra_sources

jit_sources_full = [
    "torch/csrc/jit/codegen/cuda/interface.cpp",
    "torch/csrc/jit/passes/lower_graph.cpp",
    "torch/csrc/jit/runtime/register_c10_ops.cpp",
    "torch/csrc/jit/runtime/register_prim_ops.cpp",
    "torch/csrc/jit/runtime/register_prim_ops_fulljit.cpp",
    "torch/csrc/jit/runtime/register_special_ops.cpp",
    "torch/csrc/jit/passes/remove_inplace_ops.cpp",
    "torch/csrc/jit/passes/utils/check_alias_annotation.cpp",
    "torch/csrc/jit/passes/autocast.cpp",
]

libtorch_core_jit_sources = sorted(jit_sources_full)

torch_mobile_tracer_sources = [
    "torch/csrc/jit/mobile/model_tracer/tracer.cpp",
    "torch/csrc/jit/mobile/model_tracer/TensorUtils.cpp",
    "torch/csrc/jit/mobile/model_tracer/TracerRunner.cpp",
    "torch/csrc/jit/mobile/model_tracer/MobileModelRunner.cpp",
    "torch/csrc/jit/mobile/model_tracer/OperatorCallTracer.cpp",
    "torch/csrc/jit/mobile/model_tracer/KernelDTypeTracer.cpp",
    "torch/csrc/jit/mobile/model_tracer/CustomClassTracer.cpp",
    "torch/csrc/jit/mobile/model_tracer/BuildFeatureTracer.cpp",
]

libtorch_lite_eager_symbolication = [
    "torch/csrc/jit/frontend/source_range.cpp",
    "torch/csrc/jit/ir/scope.cpp",
    "torch/csrc/jit/mobile/debug_info.cpp",
    "torch/csrc/jit/serialization/callstack_debug_info_serialization.cpp",
    "torch/csrc/jit/serialization/source_range_serialization.cpp",
    # Later we can split serialization and deserialization logic
    # to have better separation within build and only build relevant parts.
    "torch/csrc/jit/serialization/pickle.cpp",
    "torch/csrc/jit/serialization/pickler.cpp",
    "torch/csrc/jit/serialization/unpickler.cpp",
]

# TODO: core_trainer_sources is not necessary for libtorch lite
libtorch_lite_cmake_sources = sorted(
    core_trainer_sources +
    core_sources_common +
    torch_unpickler_common +
    torch_mobile_core,
)

libtorch_cmake_sources = libtorch_core_sources + libtorch_core_jit_sources

libtorch_extra_sources = libtorch_core_jit_sources + [
    "torch/csrc/autograd/TraceTypeManual.cpp",
    "torch/csrc/autograd/VariableTypeManual.cpp",
    "torch/csrc/autograd/FunctionsManual.cpp",
    "torch/csrc/jit/api/module_save.cpp",
    "torch/csrc/jit/codegen/fuser/cpu/fused_kernel.cpp",
    "torch/csrc/jit/mobile/compatibility/backport.cpp",
    "torch/csrc/jit/mobile/compatibility/backport_manager.cpp",
    "torch/csrc/jit/mobile/compatibility/model_compatibility.cpp",
    # To be included for eager symbolication in lite interpreter
    # when it is built in libtorch
    "torch/csrc/jit/mobile/debug_info.cpp",
    "torch/csrc/jit/mobile/function.cpp",
    "torch/csrc/jit/mobile/flatbuffer_loader.cpp",
    "torch/csrc/jit/mobile/import.cpp",
    "torch/csrc/jit/mobile/import_data.cpp",
    "torch/csrc/jit/mobile/interpreter.cpp",
    "torch/csrc/jit/mobile/module.cpp",
    "torch/csrc/jit/mobile/observer.cpp",
    "torch/csrc/jit/mobile/parse_bytecode.cpp",
    "torch/csrc/jit/mobile/parse_operators.cpp",
    "torch/csrc/jit/mobile/quantization.cpp",
    "torch/csrc/jit/mobile/train/export_data.cpp",
    "torch/csrc/jit/mobile/train/optim/sgd.cpp",
    "torch/csrc/jit/mobile/train/random.cpp",
    "torch/csrc/jit/mobile/train/sequential.cpp",
    "torch/csrc/jit/mobile/upgrader_mobile.cpp",
    "torch/csrc/jit/serialization/onnx.cpp",
    "torch/csrc/jit/serialization/export.cpp",
    "torch/csrc/jit/serialization/export_bytecode.cpp",
    "torch/csrc/jit/serialization/export_module.cpp",
    "torch/csrc/jit/serialization/flatbuffer_serializer.cpp",
    "torch/csrc/jit/serialization/import_legacy.cpp",
    "torch/csrc/utils/byte_order.cpp",
    "torch/csrc/utils/out_types.cpp",
]

def libtorch_sources(gencode_pattern = ":generate-code[{}]"):
    return (
        libtorch_generated_sources(gencode_pattern) + libtorch_core_sources + libtorch_distributed_sources + libtorch_extra_sources
    )

libtorch_cuda_core_sources = [
    "torch/csrc/CudaIPCTypes.cpp",
    "torch/csrc/cuda/comm.cpp",
    "torch/csrc/cuda/memory_snapshot.cpp",
    "torch/csrc/jit/codegen/fuser/cuda/fused_kernel.cpp",
    "torch/csrc/profiler/stubs/cuda.cpp",
    "torch/csrc/autograd/functions/comm.cpp",
    "torch/csrc/jit/passes/frozen_conv_add_relu_fusion_cuda.cpp",
    "torch/csrc/jit/tensorexpr/cuda_codegen.cpp",
    "torch/csrc/jit/runtime/register_cuda_ops.cpp",
]

# These files are the only ones that are supported on Windows.
libtorch_cuda_distributed_base_sources = [
    "torch/csrc/distributed/c10d/reducer_cuda.cpp",
]

# These files are only supported on Linux (and others) but not on Windows.
libtorch_cuda_distributed_extra_sources = [
    "torch/csrc/distributed/c10d/NCCLUtils.cpp",
    "torch/csrc/distributed/c10d/ProcessGroupNCCL.cpp",
    "torch/csrc/distributed/c10d/ProcessGroupUCC.cpp",
    "torch/csrc/distributed/c10d/UCCTracing.cpp",
    "torch/csrc/distributed/c10d/UCCUtils.cpp",
    "torch/csrc/distributed/rpc/tensorpipe_cuda.cpp",
    "torch/csrc/distributed/c10d/quantization/quantization_gpu.cu",
]

libtorch_cuda_distributed_sources = libtorch_cuda_distributed_base_sources + libtorch_cuda_distributed_extra_sources

libtorch_cuda_sources = libtorch_cuda_core_sources + libtorch_cuda_distributed_sources + [
    "torch/csrc/cuda/nccl.cpp",
]

torch_cpp_srcs = [
    "torch/csrc/api/src/cuda.cpp",  # this just forwards stuff, no real CUDA
    "torch/csrc/api/src/data/datasets/mnist.cpp",
    "torch/csrc/api/src/data/samplers/distributed.cpp",
    "torch/csrc/api/src/data/samplers/random.cpp",
    "torch/csrc/api/src/data/samplers/sequential.cpp",
    "torch/csrc/api/src/data/samplers/stream.cpp",
    "torch/csrc/api/src/enum.cpp",
    "torch/csrc/api/src/imethod.cpp",
    "torch/csrc/api/src/jit.cpp",
    "torch/csrc/api/src/serialize.cpp",
    "torch/csrc/api/src/nn/init.cpp",
    "torch/csrc/api/src/nn/module.cpp",
    "torch/csrc/api/src/nn/modules/_functions.cpp",
    "torch/csrc/api/src/nn/modules/activation.cpp",
    "torch/csrc/api/src/nn/modules/adaptive.cpp",
    "torch/csrc/api/src/nn/modules/batchnorm.cpp",
    "torch/csrc/api/src/nn/modules/normalization.cpp",
    "torch/csrc/api/src/nn/modules/instancenorm.cpp",
    "torch/csrc/api/src/nn/modules/conv.cpp",
    "torch/csrc/api/src/nn/modules/dropout.cpp",
    "torch/csrc/api/src/nn/modules/distance.cpp",
    "torch/csrc/api/src/nn/modules/embedding.cpp",
    "torch/csrc/api/src/nn/modules/fold.cpp",
    "torch/csrc/api/src/nn/modules/linear.cpp",
    "torch/csrc/api/src/nn/modules/loss.cpp",
    "torch/csrc/api/src/nn/modules/padding.cpp",
    "torch/csrc/api/src/nn/modules/pixelshuffle.cpp",
    "torch/csrc/api/src/nn/modules/pooling.cpp",
    "torch/csrc/api/src/nn/modules/rnn.cpp",
    "torch/csrc/api/src/nn/modules/upsampling.cpp",
    "torch/csrc/api/src/nn/modules/transformer.cpp",
    "torch/csrc/api/src/nn/modules/container/functional.cpp",
    "torch/csrc/api/src/nn/options/activation.cpp",
    "torch/csrc/api/src/nn/options/adaptive.cpp",
    "torch/csrc/api/src/nn/options/batchnorm.cpp",
    "torch/csrc/api/src/nn/options/conv.cpp",
    "torch/csrc/api/src/nn/options/dropout.cpp",
    "torch/csrc/api/src/nn/options/instancenorm.cpp",
    "torch/csrc/api/src/nn/options/linear.cpp",
    "torch/csrc/api/src/nn/options/normalization.cpp",
    "torch/csrc/api/src/nn/options/embedding.cpp",
    "torch/csrc/api/src/nn/options/padding.cpp",
    "torch/csrc/api/src/nn/options/pooling.cpp",
    "torch/csrc/api/src/nn/options/rnn.cpp",
    "torch/csrc/api/src/nn/options/vision.cpp",
    "torch/csrc/api/src/nn/options/transformer.cpp",
    "torch/csrc/api/src/optim/adagrad.cpp",
    "torch/csrc/api/src/optim/adam.cpp",
    "torch/csrc/api/src/optim/adamw.cpp",
    "torch/csrc/api/src/optim/lbfgs.cpp",
    "torch/csrc/api/src/optim/optimizer.cpp",
    "torch/csrc/api/src/optim/rmsprop.cpp",
    "torch/csrc/api/src/optim/serialize.cpp",
    "torch/csrc/api/src/optim/sgd.cpp",
    "torch/csrc/api/src/optim/schedulers/lr_scheduler.cpp",
    "torch/csrc/api/src/optim/schedulers/step_lr.cpp",
    "torch/csrc/api/src/serialize/input-archive.cpp",
    "torch/csrc/api/src/serialize/output-archive.cpp",
]

libtorch_python_cuda_core_sources = [
    "torch/csrc/cuda/Event.cpp",
    "torch/csrc/cuda/Module.cpp",
    "torch/csrc/cuda/python_comm.cpp",
    "torch/csrc/cuda/Stream.cpp",
    "torch/csrc/cuda/Graph.cpp",
    "torch/csrc/cuda/shared/cudart.cpp",
    "torch/csrc/cuda/shared/nvtx.cpp",
    "torch/csrc/cuda/utils.cpp",
    "torch/csrc/cuda/CUDAPluggableAllocator.cpp",
]

libtorch_python_cuda_sources = libtorch_python_cuda_core_sources + [
    "torch/csrc/cuda/python_nccl.cpp",
    "torch/csrc/cuda/shared/cudnn.cpp",
    "torch/csrc/cuda/Tensor.cpp",
]

libtorch_python_core_sources = [
    "torch/csrc/DataLoader.cpp",
    "torch/csrc/Device.cpp",
    "torch/csrc/Dtype.cpp",
    "torch/csrc/DynamicTypes.cpp",
    "torch/csrc/Exceptions.cpp",
    "torch/csrc/Generator.cpp",
    "torch/csrc/Layout.cpp",
    "torch/csrc/MemoryFormat.cpp",
    "torch/csrc/QScheme.cpp",
    "torch/csrc/Module.cpp",
    "torch/csrc/PyInterpreter.cpp",
    "torch/csrc/python_dimname.cpp",
    "torch/csrc/Size.cpp",
    "torch/csrc/Storage.cpp",
    "torch/csrc/StorageMethods.cpp",
    "torch/csrc/StorageSharing.cpp",
    "torch/csrc/Stream.cpp",
    "torch/csrc/TypeInfo.cpp",
    "torch/csrc/api/src/python/init.cpp",
    "torch/csrc/autograd/functions/init.cpp",
    "torch/csrc/autograd/init.cpp",
    "torch/csrc/autograd/profiler_python.cpp",
    "torch/csrc/autograd/python_anomaly_mode.cpp",
    "torch/csrc/autograd/python_saved_variable_hooks.cpp",
    "torch/csrc/autograd/python_cpp_function.cpp",
    "torch/csrc/autograd/python_engine.cpp",
    "torch/csrc/autograd/python_function.cpp",
    "torch/csrc/autograd/python_hook.cpp",
    "torch/csrc/autograd/python_legacy_variable.cpp",
    "torch/csrc/autograd/python_nested_functions_manual.cpp",
    "torch/csrc/autograd/python_torch_functions_manual.cpp",
    "torch/csrc/autograd/python_variable.cpp",
    "torch/csrc/autograd/python_variable_indexing.cpp",
    "torch/csrc/dynamo/eval_frame.c",
    "torch/csrc/dynamo/guards.cpp",
    "torch/csrc/dynamo/init.cpp",
    "torch/csrc/functorch/init.cpp",
    "torch/csrc/mps/Module.cpp",
    "torch/csrc/jit/backends/backend_init.cpp",
    "torch/csrc/jit/python/init.cpp",
    "torch/csrc/jit/passes/onnx.cpp",
    "torch/csrc/jit/passes/onnx/cast_all_constant_to_floating.cpp",
    "torch/csrc/jit/passes/onnx/deduplicate_initializers.cpp",
    "torch/csrc/jit/passes/onnx/eval_peephole.cpp",
    "torch/csrc/jit/passes/onnx/constant_fold.cpp",
    "torch/csrc/jit/passes/onnx/constant_map.cpp",
    "torch/csrc/jit/passes/onnx/eliminate_unused_items.cpp",
    "torch/csrc/jit/passes/onnx/fixup_onnx_controlflow.cpp",
    "torch/csrc/jit/passes/onnx/list_model_parameters.cpp",
    "torch/csrc/jit/passes/onnx/function_substitution.cpp",
    "torch/csrc/jit/passes/onnx/helper.cpp",
    "torch/csrc/jit/passes/onnx/peephole.cpp",
    "torch/csrc/jit/passes/onnx/preprocess_for_onnx.cpp",
    "torch/csrc/jit/passes/onnx/prepare_division_for_onnx.cpp",
    "torch/csrc/jit/passes/onnx/scalar_type_analysis.cpp",
    "torch/csrc/jit/passes/onnx/unpack_quantized_weights.cpp",
    "torch/csrc/jit/passes/onnx/remove_inplace_ops_for_onnx.cpp",
    "torch/csrc/jit/passes/onnx/shape_type_inference.cpp",
    "torch/csrc/jit/passes/onnx/function_extraction.cpp",
    "torch/csrc/jit/passes/onnx/onnx_log.cpp",
    "torch/csrc/jit/passes/onnx/naming.cpp",
    "torch/csrc/jit/python/pybind_utils.cpp",
    "torch/csrc/jit/passes/onnx/pattern_conversion/autograd_function_process.cpp",
    "torch/csrc/jit/passes/onnx/pattern_conversion/common.cpp",
    "torch/csrc/jit/passes/onnx/pattern_conversion/pattern_encapsulation.cpp",
    "torch/csrc/jit/passes/onnx/pattern_conversion/pattern_conversion.cpp",
    "torch/csrc/jit/python/python_arg_flatten.cpp",
    "torch/csrc/jit/python/python_custom_class.cpp",
    "torch/csrc/jit/python/python_dict.cpp",
    "torch/csrc/jit/python/python_interpreter.cpp",
    "torch/csrc/jit/python/python_ir.cpp",
    "torch/csrc/jit/python/python_list.cpp",
    "torch/csrc/jit/python/python_tracer.cpp",
    "torch/csrc/jit/python/script_init.cpp",
    "torch/csrc/jit/frontend/concrete_module_type.cpp",
    "torch/csrc/jit/frontend/tree_views.cpp",
    "torch/csrc/jit/python/python_sugared_value.cpp",
    "torch/csrc/jit/python/python_tree_views.cpp",
    "torch/csrc/jit/runtime/static/init.cpp",
    "torch/csrc/jit/tensorexpr/tensorexpr_init.cpp",
    "torch/csrc/monitor/python_init.cpp",
    "torch/csrc/multiprocessing/init.cpp",
    "torch/csrc/onnx/init.cpp",
    "torch/csrc/profiler/python/init.cpp",
    "torch/csrc/serialization.cpp",
    "torch/csrc/tensor/python_tensor.cpp",
    "torch/csrc/utils/init.cpp",
    "torch/csrc/utils/throughput_benchmark.cpp",
    "torch/csrc/utils.cpp",
    "torch/csrc/utils/cuda_lazy_init.cpp",
    "torch/csrc/utils/invalid_arguments.cpp",
    "torch/csrc/utils/nested.cpp",
    "torch/csrc/utils/object_ptr.cpp",
    "torch/csrc/utils/python_arg_parser.cpp",
    "torch/csrc/utils/python_dispatch.cpp",
    "torch/csrc/utils/python_symnode.cpp",
    "torch/csrc/utils/pybind.cpp",
    "torch/csrc/utils/structseq.cpp",
    "torch/csrc/utils/tensor_apply.cpp",
    "torch/csrc/utils/tensor_dtypes.cpp",
    "torch/csrc/utils/tensor_layouts.cpp",
    "torch/csrc/utils/tensor_memoryformats.cpp",
    "torch/csrc/utils/tensor_qschemes.cpp",
    "torch/csrc/utils/tensor_list.cpp",
    "torch/csrc/utils/tensor_new.cpp",
    "torch/csrc/utils/tensor_numpy.cpp",
    "torch/csrc/utils/tensor_types.cpp",
    "torch/csrc/utils/disable_torch_function.cpp",
    "torch/csrc/utils/verbose.cpp",
] + lazy_tensor_core_python_sources

libtorch_python_distributed_core_sources = [
    "torch/csrc/distributed/c10d/init.cpp",
    "torch/csrc/distributed/c10d/python_comm_hook.cpp",
]

libtorch_python_distributed_sources = libtorch_python_distributed_core_sources + [
    "torch/csrc/distributed/autograd/init.cpp",
    "torch/csrc/distributed/rpc/init.cpp",
    "torch/csrc/distributed/rpc/py_rref.cpp",
    "torch/csrc/distributed/rpc/python_functions.cpp",
    "torch/csrc/distributed/rpc/python_rpc_handler.cpp",
    "torch/csrc/distributed/rpc/request_callback_impl.cpp",
    "torch/csrc/distributed/rpc/testing/init.cpp",
    "torch/csrc/distributed/rpc/unpickled_python_call.cpp",
    "torch/csrc/distributed/rpc/unpickled_python_remote_call.cpp",
    "torch/csrc/jit/runtime/register_distributed_ops.cpp",
]

def glob_libtorch_python_sources(gencode_pattern = ":generate-code[{}]"):
    _libtorch_python_sources = [gencode_pattern.format(name) for name in [
        "torch/csrc/autograd/generated/python_functions_0.cpp",
        "torch/csrc/autograd/generated/python_functions_1.cpp",
        "torch/csrc/autograd/generated/python_functions_2.cpp",
        "torch/csrc/autograd/generated/python_functions_3.cpp",
        "torch/csrc/autograd/generated/python_functions_4.cpp",
        "torch/csrc/autograd/generated/python_nested_functions.cpp",
        "torch/csrc/autograd/generated/python_nn_functions.cpp",
        "torch/csrc/autograd/generated/python_fft_functions.cpp",
        "torch/csrc/autograd/generated/python_linalg_functions.cpp",
        "torch/csrc/autograd/generated/python_enum_tag.cpp",
        "torch/csrc/autograd/generated/python_return_types.cpp",
        "torch/csrc/autograd/generated/python_sparse_functions.cpp",
        "torch/csrc/autograd/generated/python_special_functions.cpp",
        "torch/csrc/autograd/generated/python_torch_functions_0.cpp",
        "torch/csrc/autograd/generated/python_torch_functions_1.cpp",
        "torch/csrc/autograd/generated/python_torch_functions_2.cpp",
        "torch/csrc/autograd/generated/python_variable_methods.cpp",
    ]]

    _libtorch_python_sources.extend(libtorch_python_core_sources)
    _libtorch_python_sources.extend(libtorch_python_distributed_sources)

    return _libtorch_python_sources

# List of non-globed source used to build ATen core internally
aten_cpu_non_globed_sources = [
    "aten/src/ATen/detail/CUDAHooksInterface.cpp",
    "aten/src/ATen/detail/HIPHooksInterface.cpp",
    "aten/src/ATen/detail/MPSHooksInterface.cpp",
    "aten/src/ATen/detail/ORTHooksInterface.cpp",
    "aten/src/ATen/record_function.cpp",
    "aten/src/ATen/Dispatch.cpp",
    "aten/src/ATen/SequenceNumber.cpp",
]

aten_cpu_non_globed_headers = [
    "aten/src/ATen/CPUGeneratorImpl.h",
    "aten/src/ATen/NumericUtils.h",
    "aten/src/ATen/detail/CUDAHooksInterface.h",
    "aten/src/ATen/detail/MPSHooksInterface.h",
    "aten/src/ATen/detail/HIPHooksInterface.h",
    "aten/src/ATen/detail/ORTHooksInterface.h",
]

aten_cpu_source_non_codegen_list = [
    "aten/src/ATen/AccumulateType.cpp",
    "aten/src/ATen/LegacyBatchedTensorImpl.cpp",
    "aten/src/ATen/CPUGeneratorImpl.cpp",
    "aten/src/ATen/Context.cpp",
    "aten/src/ATen/DLConvertor.cpp",
    "aten/src/ATen/EmptyTensor.cpp",
    "aten/src/ATen/ExpandUtils.cpp",
    "aten/src/ATen/FunctionalInverses.cpp",
    "aten/src/ATen/FunctionalStorageImpl.cpp",
    "aten/src/ATen/FunctionalTensorWrapper.cpp",
    "aten/src/ATen/FunctionalizeFallbackKernel.cpp",
    "aten/src/ATen/MemoryOverlap.cpp",
    "aten/src/ATen/MapAllocator.cpp",
    "aten/src/ATen/NamedTensorUtils.cpp",
    "aten/src/ATen/NestedTensorImpl.cpp",
    "aten/src/ATen/ParallelCommon.cpp",
    "aten/src/ATen/ParallelNative.cpp",
    "aten/src/ATen/ParallelNativeTBB.cpp",
    "aten/src/ATen/ParallelOpenMP.cpp",
    "aten/src/ATen/ParallelThreadPoolNative.cpp",
    "aten/src/ATen/PythonTorchFunctionTLS.cpp",
    "aten/src/ATen/ThreadLocalPythonObjects.cpp",
    "aten/src/ATen/ScalarOps.cpp",
    "aten/src/ATen/SparseTensorImpl.cpp",
    "aten/src/ATen/SparseCsrTensorImpl.cpp",
    "aten/src/ATen/SparseTensorUtils.cpp",
    "aten/src/ATen/TensorGeometry.cpp",
    "aten/src/ATen/TensorIndexing.cpp",
    "aten/src/ATen/TensorMeta.cpp",
    "aten/src/ATen/TensorNames.cpp",
    "aten/src/ATen/TensorUtils.cpp",
    "aten/src/ATen/ThreadLocalState.cpp",
    "aten/src/ATen/FuncTorchTLS.cpp",
    "aten/src/ATen/Utils.cpp",
    "aten/src/ATen/Version.cpp",
    "aten/src/ATen/LegacyVmapMode.cpp",
    "aten/src/ATen/LegacyVmapTransforms.cpp",
    "aten/src/ATen/core/BackendSelectFallbackKernel.cpp",
    "aten/src/ATen/core/DeprecatedTypeProperties.cpp",
    "aten/src/ATen/core/DeprecatedTypePropertiesRegistry.cpp",
    "aten/src/ATen/core/Dict.cpp",
    "aten/src/ATen/core/Dimname.cpp",
    "aten/src/ATen/core/Formatting.cpp",
    "aten/src/ATen/core/function_schema.cpp",
    "aten/src/ATen/core/Generator.cpp",
    "aten/src/ATen/core/List.cpp",
    "aten/src/ATen/core/NamedTensor.cpp",
    "aten/src/ATen/core/Tensor.cpp",
    "aten/src/ATen/core/VariableFallbackKernel.cpp",
    "aten/src/ATen/core/VariableHooksInterface.cpp",
    "aten/src/ATen/core/Vitals.cpp",
    "aten/src/ATen/core/boxing/KernelFunction.cpp",
    "aten/src/ATen/core/custom_class.cpp",
    "aten/src/ATen/core/dispatch/DispatchKeyExtractor.cpp",
    "aten/src/ATen/core/dispatch/Dispatcher.cpp",
    "aten/src/ATen/core/dispatch/ObservedOperators.cpp",
    "aten/src/ATen/core/dispatch/OperatorEntry.cpp",
    "aten/src/ATen/core/interned_strings.cpp",
    "aten/src/ATen/core/ivalue.cpp",
    "aten/src/ATen/core/library.cpp",
    "aten/src/ATen/core/op_registration/infer_schema.cpp",
    "aten/src/ATen/core/op_registration/op_registration.cpp",
    "aten/src/ATen/core/operator_name.cpp",
    "aten/src/ATen/core/TorchDispatchUtils.cpp",
    "aten/src/ATen/core/register_symbols.cpp",
    "aten/src/ATen/core/class_type.cpp",
    "aten/src/ATen/core/type.cpp",
    "aten/src/ATen/core/type_factory.cpp",
    "aten/src/ATen/core/dynamic_type.cpp",
    "aten/src/ATen/core/tensor_type.cpp",
    "aten/src/ATen/core/union_type.cpp",
    "aten/src/ATen/cpu/FlushDenormal.cpp",
    "aten/src/ATen/detail/CPUGuardImpl.cpp",
    "aten/src/ATen/metal/Context.cpp",
    "aten/src/ATen/native/AutogradComposite.cpp",
    "aten/src/ATen/native/ComparisonUtils.cpp",
    "aten/src/ATen/native/DispatchStub.cpp",
    "aten/src/ATen/native/UpSample.cpp",
    "aten/src/ATen/native/mkldnn/BinaryOps.cpp",
    "aten/src/ATen/native/mkldnn/Conv.cpp",
    "aten/src/ATen/native/mkldnn/ConvPrepack.cpp",
    "aten/src/ATen/native/mkldnn/Copy.cpp",
    "aten/src/ATen/native/mkldnn/Gelu.cpp",
    "aten/src/ATen/native/mkldnn/IDeepRegistration.cpp",
    "aten/src/ATen/native/mkldnn/Linear.cpp",
    "aten/src/ATen/native/mkldnn/MKLDNNCommon.cpp",
    "aten/src/ATen/native/mkldnn/MKLDNNConversions.cpp",
    "aten/src/ATen/native/mkldnn/MkldnnTensorMath.cpp",
    "aten/src/ATen/native/mkldnn/Normalization.cpp",
    "aten/src/ATen/native/mkldnn/OpContext.cpp",
    "aten/src/ATen/native/mkldnn/Pooling.cpp",
    "aten/src/ATen/native/mkldnn/Prelu.cpp",
    "aten/src/ATen/native/mkldnn/RegisterMkldnnOpContextClass.cpp",
    "aten/src/ATen/native/mkldnn/Relu.cpp",
    "aten/src/ATen/native/mkldnn/RNN.cpp",
    "aten/src/ATen/native/mkldnn/SoftMax.cpp",
    "aten/src/ATen/native/mkldnn/TensorFactories.cpp",
    "aten/src/ATen/native/mkldnn/TensorShape.cpp",
    "aten/src/ATen/native/mkldnn/UnaryOps.cpp",
    "aten/src/ATen/native/mkldnn/Utils.cpp",
    "aten/src/ATen/native/mkldnn/Matmul.cpp",
    "aten/src/ATen/native/quantized/cpu/init_qnnpack.cpp",
    # This is moved to aten_cpu because some of the custom ops use empty_with_tail_padding
    # which was available only within aten_native_cpu. Ideally the right fix is to make
    # empty_with_tail_padding into an op and use dispatcher with it. But exposing it as an op
    # has limited use and hence does not seem to really make sense.
    "aten/src/ATen/native/utils/Factory.cpp",
    "aten/src/ATen/SavedTensorHooks.cpp",
    "aten/src/ATen/vulkan/Context.cpp",
    "aten/src/ATen/native/prim_native_functions.cpp",
    "aten/src/ATen/native/verbose_wrapper.cpp",
] + aten_cpu_non_globed_sources

aten_cpu_source_codegen_list = [
    "aten/src/ATen/native/cpu/AdaptiveAvgPoolKernel.cpp",
    "aten/src/ATen/native/cpu/AdaptiveMaxPoolKernel.cpp",
]

aten_ufunc_headers = [
    "aten/src/ATen/native/ufunc/add.h",
]

# When building lite interpreter in OSS, "aten/src/ATen/native/cpu/AdaptiveAvgPoolKernel.cpp" will go through
# codegen process. The codegen version of this file, like Activation.cpp.DEFAULT.cpp, will be included
# in ${cpu_kernel_cpp} in aten/src/ATen/CMakeLists.txt. As a result, in aten/src/ATen/CMakeLists.txt,
# only aten_cpu_source_non_codegen_list need to be added to ${all_cpu_cpp}.
aten_cpu_source_list = sorted(aten_cpu_source_non_codegen_list + aten_cpu_source_codegen_list)

# Same as ${aten_cpu_source_codegen_list}, this list will go through aten codegen, and be included in
# ${cpu_kernel_cpp} in aten/src/ATen/CMakeLists.txt.
aten_native_source_codegen_list = [
    "aten/src/ATen/native/cpu/Activation.cpp",
    "aten/src/ATen/native/cpu/AvgPoolKernel.cpp",
    "aten/src/ATen/native/cpu/BinaryOpsKernel.cpp",
    "aten/src/ATen/native/cpu/BlasKernel.cpp",
    "aten/src/ATen/native/cpu/CatKernel.cpp",
    "aten/src/ATen/native/cpu/ChannelShuffleKernel.cpp",
    "aten/src/ATen/native/cpu/ComplexKernel.cpp",
    "aten/src/ATen/native/cpu/CopyKernel.cpp",
    "aten/src/ATen/native/cpu/CrossKernel.cpp",
    "aten/src/ATen/native/cpu/DepthwiseConvKernel.cpp",
    "aten/src/ATen/native/cpu/DistanceOpsKernel.cpp",
    "aten/src/ATen/native/cpu/DistributionKernels.cpp",
    "aten/src/ATen/native/cpu/FillKernel.cpp",
    "aten/src/ATen/native/cpu/FunctionOfAMatrixUtilsKernel.cpp",
    "aten/src/ATen/native/cpu/GridSamplerKernel.cpp",
    "aten/src/ATen/native/cpu/HistogramKernel.cpp",
    "aten/src/ATen/native/cpu/IndexKernel.cpp",
    "aten/src/ATen/native/cpu/LerpKernel.cpp",
    "aten/src/ATen/native/cpu/LinearAlgebraKernel.cpp",
    "aten/src/ATen/native/cpu/MaxPoolKernel.cpp",
    "aten/src/ATen/native/cpu/MaxPooling.cpp",
    "aten/src/ATen/native/cpu/MaxUnpoolKernel.cpp",
    "aten/src/ATen/native/cpu/MultinomialKernel.cpp",
    "aten/src/ATen/native/cpu/PixelShuffleKernel.cpp",
    "aten/src/ATen/native/cpu/PointwiseOpsKernel.cpp",
    "aten/src/ATen/native/cpu/PowKernel.cpp",
    "aten/src/ATen/native/cpu/RangeFactoriesKernel.cpp",
    "aten/src/ATen/native/cpu/ReduceAllOpsKernel.cpp",
    "aten/src/ATen/native/cpu/ReduceOpsKernel.cpp",
    "aten/src/ATen/native/cpu/RenormKernel.cpp",
    "aten/src/ATen/native/cpu/ScatterGatherKernel.cpp",
    "aten/src/ATen/native/cpu/SoftMaxKernel.cpp",
    "aten/src/ATen/native/cpu/SortingKernel.cpp",
    "aten/src/ATen/native/cpu/StackKernel.cpp",
    "aten/src/ATen/native/cpu/SumKernel.cpp",
    "aten/src/ATen/native/cpu/TensorCompareKernel.cpp",
    "aten/src/ATen/native/cpu/UnaryOpsKernel.cpp",
    "aten/src/ATen/native/cpu/Unfold2d.cpp",
    "aten/src/ATen/native/cpu/UnfoldBackwardKernel.cpp",
    "aten/src/ATen/native/cpu/UpSampleKernel.cpp",
    "aten/src/ATen/native/cpu/UpSampleMoreKernel.cpp",
    "aten/src/ATen/native/cpu/WeightNormKernel.cpp",
    "aten/src/ATen/native/cpu/airy_ai.cpp",
    "aten/src/ATen/native/cpu/batch_norm_kernel.cpp",
    "aten/src/ATen/native/cpu/group_norm_kernel.cpp",
    "aten/src/ATen/native/cpu/layer_norm_kernel.cpp",
    "aten/src/ATen/native/cpu/scaled_modified_bessel_k0.cpp",
    "aten/src/ATen/native/cpu/scaled_modified_bessel_k1.cpp",
    "aten/src/ATen/native/cpu/spherical_bessel_j0.cpp",
    "aten/src/ATen/native/cpu/SampledAddmmKernel.cpp",
    "aten/src/ATen/native/cpu/SpmmReduceKernel.cpp",
    "aten/src/ATen/native/cpu/SparseFactories.cpp",
    "aten/src/ATen/native/quantized/cpu/kernels/QuantizedOpKernels.cpp",
]

# This aten native source file list will not go through aten codegen process
aten_native_source_non_codegen_list = [
    "aten/src/ATen/native/ao_sparse/library.cpp",
    "aten/src/ATen/native/ao_sparse/quantized/cpu/fbgemm_utils.cpp",
    "aten/src/ATen/native/ao_sparse/quantized/cpu/qlinear.cpp",
    "aten/src/ATen/native/ao_sparse/quantized/cpu/qlinear_deserialize.cpp",
    "aten/src/ATen/native/ao_sparse/quantized/cpu/qlinear_dynamic.cpp",
    "aten/src/ATen/native/ao_sparse/quantized/cpu/qlinear_prepack.cpp",
    "aten/src/ATen/native/ao_sparse/quantized/cpu/qlinear_serialize.cpp",
    "aten/src/ATen/native/ao_sparse/quantized/cpu/qlinear_unpack.cpp",
    "aten/src/ATen/native/quantized/cpu/fbgemm_utils.cpp",
    "aten/src/ATen/native/quantized/cpu/fused_obs_fake_quant.cpp",
    "aten/src/ATen/native/quantized/cpu/IntReprQuant.cpp",
    "aten/src/ATen/native/quantized/cpu/MakePerTensorQuantizedTensor.cpp",
    "aten/src/ATen/native/quantized/cpu/AdaptiveAveragePooling.cpp",
    "aten/src/ATen/native/quantized/cpu/AveragePool2d.cpp",
    "aten/src/ATen/native/quantized/cpu/AveragePool3d.cpp",
    "aten/src/ATen/native/quantized/cpu/BinaryOps.cpp",
    "aten/src/ATen/native/quantized/cpu/Normalization.cpp",
    "aten/src/ATen/native/quantized/cpu/ChannelShuffle.cpp",
    "aten/src/ATen/native/quantized/cpu/qclamp.cpp",
    "aten/src/ATen/native/quantized/cpu/TensorShape.cpp",
    "aten/src/ATen/native/quantized/cpu/qconv.cpp",
    "aten/src/ATen/native/quantized/cpu/qconv_prepack.cpp",
    "aten/src/ATen/native/quantized/cpu/qconv_unpack_impl.cpp",
    "aten/src/ATen/native/quantized/cpu/qelu.cpp",
    "aten/src/ATen/native/quantized/cpu/qembeddingbag.cpp",
    "aten/src/ATen/native/quantized/cpu/qembeddingbag_prepack.cpp",
    "aten/src/ATen/native/quantized/cpu/qembeddingbag_unpack.cpp",
    "aten/src/ATen/native/quantized/cpu/qgelu.cpp",
    "aten/src/ATen/native/quantized/cpu/qhardsigmoid.cpp",
    "aten/src/ATen/native/quantized/cpu/qhardswish.cpp",
    "aten/src/ATen/native/quantized/cpu/qlinear.cpp",
    "aten/src/ATen/native/quantized/cpu/qlinear_dynamic.cpp",
    "aten/src/ATen/native/quantized/cpu/qconv_dynamic.cpp",
    "aten/src/ATen/native/quantized/cpu/qlinear_prepack.cpp",
    "aten/src/ATen/native/quantized/cpu/LinearUnpackImpl.cpp",
    "aten/src/ATen/native/quantized/cpu/qmatmul.cpp",
    "aten/src/ATen/native/quantized/cpu/qmul.cpp",
    "aten/src/ATen/native/quantized/cpu/qnormalization.cpp",
    "aten/src/ATen/native/quantized/cpu/Pooling.cpp",
    "aten/src/ATen/native/quantized/cpu/ReduceOps.cpp",
    "aten/src/ATen/native/quantized/cpu/qrelu.cpp",
    "aten/src/ATen/native/quantized/cpu/qsigmoid.cpp",
    "aten/src/ATen/native/quantized/cpu/qsoftmax.cpp",
    "aten/src/ATen/native/quantized/cpu/Sorting.cpp",
    "aten/src/ATen/native/quantized/cpu/qtanh.cpp",
    "aten/src/ATen/native/quantized/cpu/qthreshold.cpp",
    "aten/src/ATen/native/quantized/cpu/UpSampleBilinear2d.cpp",
    "aten/src/ATen/native/quantized/cpu/UpSampleNearest2d.cpp",
    "aten/src/ATen/native/quantized/cpu/UpSampleNearest3d.cpp",
    "aten/src/ATen/native/quantized/cpu/TensorOperators.cpp",
    "aten/src/ATen/native/quantized/Copy.cpp",
    "aten/src/ATen/native/quantized/QTensor.cpp",
    "aten/src/ATen/native/quantized/TensorCompare.cpp",
    "aten/src/ATen/native/quantized/TensorFactories.cpp",
    "aten/src/ATen/native/quantized/AffineQuantizer.cpp",
    "aten/src/ATen/native/quantized/AffineQuantizerBase.cpp",
    "aten/src/ATen/native/quantized/FakeQuantPerChannelAffine.cpp",
    "aten/src/ATen/native/quantized/FakeQuantPerTensorAffine.cpp",
    "aten/src/ATen/native/quantized/library.cpp",
    "aten/src/ATen/native/quantized/TensorAdvancedIndexing.cpp",
    "aten/src/ATen/native/quantized/cpu/RuyUtils.cpp",
    "aten/src/ATen/native/quantized/cpu/XnnpackUtils.cpp",
    "aten/src/ATen/native/quantized/qlinear_unpack.cpp",
    "aten/src/ATen/quantized/QTensorImpl.cpp",
    "aten/src/ATen/quantized/Quantizer.cpp",
    "aten/src/ATen/native/Activation.cpp",
    "aten/src/ATen/native/AdaptiveAveragePooling.cpp",
    "aten/src/ATen/native/AdaptiveAveragePooling3d.cpp",
    "aten/src/ATen/native/AdaptiveMaxPooling2d.cpp",
    "aten/src/ATen/native/AdaptiveMaxPooling3d.cpp",
    "aten/src/ATen/native/AffineGridGenerator.cpp",
    "aten/src/ATen/native/AveragePool2d.cpp",
    "aten/src/ATen/native/AveragePool3d.cpp",
    "aten/src/ATen/native/BatchLinearAlgebra.cpp",
    "aten/src/ATen/native/BatchLinearAlgebraKernel.cpp",
    "aten/src/ATen/native/LegacyBatching.cpp",
    "aten/src/ATen/native/BinaryOps.cpp",
    "aten/src/ATen/native/Blas.cpp",
    "aten/src/ATen/native/BlasKernel.cpp",
    "aten/src/ATen/native/Bucketization.cpp",
    "aten/src/ATen/native/CPUBlas.cpp",
    "aten/src/ATen/native/ChanelShuffle.cpp",
    "aten/src/ATen/native/Col2Im.cpp",
    "aten/src/ATen/native/PadNd.cpp",
    "aten/src/ATen/native/Convolution.cpp",
    "aten/src/ATen/native/ConvolutionMM2d.cpp",
    "aten/src/ATen/native/ConvolutionMM3d.cpp",
    "aten/src/ATen/native/ConvolutionTBC.cpp",
    "aten/src/ATen/native/Copy.cpp",
    "aten/src/ATen/native/Correlation.cpp",
    "aten/src/ATen/native/CPUFallback.cpp",
    "aten/src/ATen/native/Cross.cpp",
    "aten/src/ATen/native/DilatedMaxPool2d.cpp",
    "aten/src/ATen/native/DilatedMaxPool3d.cpp",
    # Referenced by both native and ATen/Version.cpp. Does not reference to other native symbols
    # "aten/src/ATen/native/DispatchStub.cpp",
    # "aten/src/ATen/native/quantized/cpu/init_qnnpack.cpp",
    "aten/src/ATen/native/Distance.cpp",
    "aten/src/ATen/native/Distributions.cpp",
    "aten/src/ATen/native/Dropout.cpp",
    "aten/src/ATen/native/Embedding.cpp",
    "aten/src/ATen/native/EmbeddingBag.cpp",
    "aten/src/ATen/native/Fill.cpp",
    "aten/src/ATen/native/ForeachOpsKernels.cpp",
    "aten/src/ATen/native/FractionalMaxPool2d.cpp",
    "aten/src/ATen/native/FractionalMaxPool3d.cpp",
    "aten/src/ATen/native/FunctionOfAMatrixUtils.cpp",
    "aten/src/ATen/native/GatedLinearUnit.cpp",
    "aten/src/ATen/native/GridSampler.cpp",
    "aten/src/ATen/native/Histogram.cpp",
    "aten/src/ATen/native/Im2Col.cpp",
    "aten/src/ATen/native/IndexingUtils.cpp",
    "aten/src/ATen/native/Integration.cpp",
    "aten/src/ATen/native/Itertools.cpp",
    "aten/src/ATen/native/LegacyBridge.cpp",
    "aten/src/ATen/native/Lerp.cpp",
    "aten/src/ATen/native/Linear.cpp",
    "aten/src/ATen/native/LinearAlgebra.cpp",
    "aten/src/ATen/native/Loss.cpp",
    "aten/src/ATen/native/LossCTC.cpp",
    "aten/src/ATen/native/LossMultiLabelMargin.cpp",
    "aten/src/ATen/native/LossMultiMargin.cpp",
    "aten/src/ATen/native/LossNLL.cpp",
    "aten/src/ATen/native/LossNLL2d.cpp",
    "aten/src/ATen/native/MaxPooling.cpp",
    "aten/src/ATen/native/MaxUnpooling.cpp",
    "aten/src/ATen/native/Memory.cpp",
    "aten/src/ATen/native/MetaTensor.cpp",
    "aten/src/ATen/native/NNPACK.cpp",
    "aten/src/ATen/native/NaiveConvolutionTranspose2d.cpp",
    "aten/src/ATen/native/NaiveConvolutionTranspose3d.cpp",
    "aten/src/ATen/native/NaiveDilatedConvolution.cpp",
    "aten/src/ATen/native/NamedTensor.cpp",
    "aten/src/ATen/native/Normalization.cpp",
    "aten/src/ATen/native/Onehot.cpp",
    "aten/src/ATen/native/PackedSequence.cpp",
    "aten/src/ATen/native/PixelShuffle.cpp",
    "aten/src/ATen/native/PointwiseOps.cpp",
    "aten/src/ATen/native/Pooling.cpp",
    "aten/src/ATen/native/Pow.cpp",
    "aten/src/ATen/native/QuantizedLinear.cpp",
    "aten/src/ATen/native/RNN.cpp",
    "aten/src/ATen/native/RangeFactories.cpp",
    "aten/src/ATen/native/ReduceAllOps.cpp",
    "aten/src/ATen/native/ReduceOps.cpp",
    "aten/src/ATen/native/ReflectionPad.cpp",
    "aten/src/ATen/native/Repeat.cpp",
    "aten/src/ATen/native/ReplicationPadding.cpp",
    "aten/src/ATen/native/Resize.cpp",
    "aten/src/ATen/native/RowwisePrune.cpp",
    "aten/src/ATen/native/SegmentReduce.cpp",
    "aten/src/ATen/native/Scalar.cpp",
    "aten/src/ATen/native/SobolEngineOps.cpp",
    "aten/src/ATen/native/SobolEngineOpsUtils.cpp",
    "aten/src/ATen/native/SoftMax.cpp",
    "aten/src/ATen/native/Sorting.cpp",
    "aten/src/ATen/native/SpectralOps.cpp",
    "aten/src/ATen/native/SummaryOps.cpp",
    "aten/src/ATen/native/TensorAdvancedIndexing.cpp",
    "aten/src/ATen/native/TensorCompare.cpp",
    "aten/src/ATen/native/TensorConversions.cpp",
    "aten/src/ATen/native/TensorFactories.cpp",
    "aten/src/ATen/native/TensorIteratorReduce.cpp",
    "aten/src/ATen/native/TensorProperties.cpp",
    "aten/src/ATen/native/TensorShape.cpp",
    "aten/src/ATen/native/TensorTransformations.cpp",
    "aten/src/ATen/native/TestOps.cpp",
    "aten/src/ATen/native/TriangularOps.cpp",
    "aten/src/ATen/native/TypeProperties.cpp",
    "aten/src/ATen/native/UnaryOps.cpp",
    "aten/src/ATen/native/Unfold2d.cpp",
    "aten/src/ATen/native/Unfold3d.cpp",
    "aten/src/ATen/native/UnfoldBackward.cpp",
    "aten/src/ATen/native/Unique.cpp",
    # Low-level functions that can be directly referenced
    # "aten/src/ATen/native/UpSample.cpp",
    "aten/src/ATen/native/UpSampleBicubic2d.cpp",
    "aten/src/ATen/native/UpSampleBilinear2d.cpp",
    "aten/src/ATen/native/UpSampleLinear1d.cpp",
    "aten/src/ATen/native/UpSampleNearest1d.cpp",
    "aten/src/ATen/native/UpSampleNearest2d.cpp",
    "aten/src/ATen/native/UpSampleNearest3d.cpp",
    "aten/src/ATen/native/UpSampleTrilinear3d.cpp",
    "aten/src/ATen/native/VariableMethodStubs.cpp",
    "aten/src/ATen/native/WeightNorm.cpp",
    "aten/src/ATen/native/group_norm.cpp",
    "aten/src/ATen/native/layer_norm.cpp",
    "aten/src/ATen/native/mkl/LinearAlgebra.cpp",
    "aten/src/ATen/native/mkl/SparseBlasImpl.cpp",
    "aten/src/ATen/native/mkl/SparseCsrLinearAlgebra.cpp",
    "aten/src/ATen/native/mkl/SpectralOps.cpp",
    "aten/src/ATen/native/nested/NestedTensorAliases.cpp",
    "aten/src/ATen/native/nested/NestedTensorBackward.cpp",
    "aten/src/ATen/native/nested/NestedTensorBinaryOps.cpp",
    "aten/src/ATen/native/nested/NestedTensorFactories.cpp",
    "aten/src/ATen/native/nested/NestedTensorMath.cpp",
    "aten/src/ATen/native/nested/NestedTensorMatmul.cpp",
    "aten/src/ATen/native/nested/NestedTensorTransformerFunctions.cpp",
    "aten/src/ATen/native/nested/NestedTensorUnaryOps.cpp",
    "aten/src/ATen/native/nested/NestedTensorUtils.cpp",
    "aten/src/ATen/native/sparse/ParamUtils.cpp",
    "aten/src/ATen/native/sparse/SoftMax.cpp",
    "aten/src/ATen/native/sparse/SparseBlas.cpp",
    "aten/src/ATen/native/sparse/SparseBlasImpl.cpp",
    "aten/src/ATen/native/sparse/SparseMatMul.cpp",
    "aten/src/ATen/native/sparse/SparseTensor.cpp",
    "aten/src/ATen/native/sparse/SparseCsrTensor.cpp",
    "aten/src/ATen/native/sparse/SparseTensorMath.cpp",
    "aten/src/ATen/native/sparse/SparseUnaryOps.cpp",
    "aten/src/ATen/native/sparse/SparseCsrTensorMath.cpp",
    "aten/src/ATen/native/sparse/SparseFactories.cpp",
    "aten/src/ATen/native/sparse/ValidateCompressedIndicesKernel.cpp",
    "aten/src/ATen/native/sparse/SparseBinaryOpIntersectionKernel.cpp",
    "aten/src/ATen/native/transformers/attention.cpp",
    "aten/src/ATen/native/transformers/transformer.cpp",
    "aten/src/ATen/native/xnnpack/Activation.cpp",
    "aten/src/ATen/native/xnnpack/ChannelShuffle.cpp",
    "aten/src/ATen/native/xnnpack/Convolution.cpp",
    "aten/src/ATen/native/xnnpack/AveragePooling.cpp",
    "aten/src/ATen/native/xnnpack/Init.cpp",
    "aten/src/ATen/native/xnnpack/Linear.cpp",
    "aten/src/ATen/native/xnnpack/MaxPooling.cpp",
    "aten/src/ATen/native/xnnpack/OpContext.cpp",
    "aten/src/ATen/native/xnnpack/RegisterOpContextClass.cpp",
    "aten/src/ATen/native/xnnpack/Shim.cpp",
    # Files not in native, but depends on native symbols
    # "aten/src/ATen/TensorIndexing.cpp",
    "aten/src/ATen/TensorIterator.cpp",
]

# 1. Files in ATen/native with a few exceptions
# TODO: move the exceptions to proper locations
# 2. The whole aten native source list includes the list with and without aten codegen process.
aten_native_source_list = sorted(aten_native_source_non_codegen_list + aten_native_source_codegen_list)

# These are cpp files which need to go in the torch_cuda_cu library
# .cu files can be found via glob
aten_cuda_cu_source_list = [
    "aten/src/ATen/cuda/CUDABlas.cpp",
    "aten/src/ATen/cuda/CUDASparseBlas.cpp",
    "aten/src/ATen/cuda/CublasHandlePool.cpp",
    "aten/src/ATen/native/cuda/Activation.cpp",
    "aten/src/ATen/native/cuda/LinearAlgebraStubs.cpp",
    "aten/src/ATen/native/cuda/Blas.cpp",
    "aten/src/ATen/native/cuda/Distributions.cpp",
    "aten/src/ATen/native/cuda/Equal.cpp",
    "aten/src/ATen/native/cuda/GridSampler.cpp",
    "aten/src/ATen/native/cuda/IndexKernel.cpp",
    "aten/src/ATen/native/cuda/ReduceOps.cpp",
    "aten/src/ATen/native/cuda/ScanKernels.cpp",
    "aten/src/ATen/native/cuda/Sort.cpp",
    "aten/src/ATen/native/cuda/Sorting.cpp",
    "aten/src/ATen/native/cuda/TensorModeKernel.cpp",
    "aten/src/ATen/native/cuda/TensorShapeCUDA.cpp",
    "aten/src/ATen/native/cuda/TensorTopK.cpp",
    "aten/src/ATen/native/cuda/jit_utils.cpp",
    "aten/src/ATen/native/nested/cuda/NestedTensorTransformerFunctions.cpp",
    "aten/src/ATen/native/sparse/cuda/SparseBlas.cpp",
    "aten/src/ATen/native/sparse/cuda/SparseBlasImpl.cpp",
    "aten/src/ATen/native/sparse/cuda/SparseBlasLegacy.cpp",
    "aten/src/ATen/native/sparse/cuda/SparseCUDABlas.cpp",
    "aten/src/ATen/native/transformers/cuda/flash_attn/fmha_api.cpp",
]

# Files using thrust::sort_by_key need to be linked last
aten_cuda_with_sort_by_key_source_list = [
    # empty_cuda is needed by torch_cuda_cpp
    "aten/src/ATen/native/cuda/TensorFactories.cu",
]

aten_cuda_cu_with_sort_by_key_source_list = [
    "aten/src/ATen/native/cuda/Unique.cu",
]

# Followings are source code for xnnpack delegate

xnnpack_delegate_serializer_header = [
    "torch/csrc/jit/backends/xnnpack/serialization/serializer.h",
]

xnnpack_delegate_serializer_source_list = [
    "torch/csrc/jit/backends/xnnpack/serialization/serializer.cpp",
]

xnnpack_delegate_core_source_list = [
    "torch/csrc/jit/backends/xnnpack/compiler/xnn_compiler.cpp",
]

xnnpack_delegate_core_header = [
    "torch/csrc/jit/backends/xnnpack/compiler/xnn_compiler.h",
    "torch/csrc/jit/backends/xnnpack/executor/xnn_executor.h",
]

xnnpack_backend_header = [
    "torch/csrc/jit/backends/xnnpack/xnnpack_graph_builder.h",
] + xnnpack_delegate_core_header

xnnpack_backend_source_list = [
    "torch/csrc/jit/backends/xnnpack/compiler/xnn_compiler.cpp",
    "torch/csrc/jit/backends/xnnpack/xnnpack_backend_lib.cpp",
    "torch/csrc/jit/backends/xnnpack/xnnpack_backend_preprocess.cpp",
    "torch/csrc/jit/backends/xnnpack/xnnpack_graph_builder.cpp",
] + xnnpack_delegate_core_source_list
