load("//tools/build_defs:glob_defs.bzl", "subdir_glob")
load(
    ":pt_ops.bzl",
    "pt_operator_library",
)
load(":buckbuild.bzl",
    "define_buck_targets",
    "get_pt_operator_registry_dict",
)

# define shared buck targets
define_buck_targets()

# define OSS only targets
cxx_library(
    name = "pthreadpool",
    srcs = ['caffe2/utils/threadpool/pthreadpool.cc', 'caffe2/utils/threadpool/pthreadpool_impl.cc', 'caffe2/utils/threadpool/pthreadpool-cpp.cc', 'caffe2/utils/threadpool/thread_pool_guard.cpp', 'caffe2/utils/threadpool/ThreadPool.cc'],
    deps = [':caffe2_headers', '//third_party:cpuinfo', '//third_party:glog', '//c10:c10', '//third_party:FXdiv'],
    exported_deps = ['//third_party:pthreadpool'],
    compiler_flags = ['-Wno-unused-function'],
    preferred_linkage = "static",
    exported_headers = subdir_glob([("", "caffe2/utils/threadpool/*.h")]),
    exported_preprocessor_flags = ['-DUSE_PTHREADPOOL'],
    header_namespace = "",
    headers = [],
    link_whole = True,
    platform_preprocessor_flags = [['windows', ['-D_WINDOWS', '-D_WIN32', '-DWIN32', '-DNOMINMAX', '-D_CRT_SECURE_NO_WARNINGS', '-D_USE_MATH_DEFINES']], ['windows.*64$', ['-D_WIN64']]],
    visibility = ['PUBLIC'],
)

cxx_library(
    name = "caffe2_headers",
    deps = ['//c10:c10'],
    exported_headers = subdir_glob(
        [
            ("", "caffe2/**/*.h"),
            ("", "binaries/**/*.h"),
            ("modules", "**/*.h"),
            ("aten/src", "ATen/core/**/*.h"),
        ],
        exclude = [
            "caffe2/fb/**/*.h",
            "caffe2/mobile/contrib/libopencl-stub/**/*.h",
            "caffe2/mobile/contrib/libvulkan-stub/**/*.h",
            "caffe2/mobile/contrib/nnapi/**/*.h",
            "caffe2/mobile/fb/binary/**/*.h",
            "caffe2/mobile/fb/snpe_so/**/*.h",
            "caffe2/mobile/fb/boltnn/bolt_lib/include/**/*.h",
            "caffe2/mobile/contrib/snpe/**/*.h",
            "caffe2/mobile/fb/qpl/jni/QuickPerformanceLogger.h",
            "caffe2/share/fb/x3d/ldi/*.h",
            "**/*.pb.h",
        ],
    ),
    compiler_flags = ['-Os', '-fexceptions', '-frtti', '-Wno-shadow', '-Wno-unknown-pragmas', '-Wno-unused-variable', '-Wno-sign-compare', '-Icaffe2', '-Imodules', '-DEIGEN_NO_DEBUG', '-DCAFFE2_USE_LITE_PROTO', '-DCAFFE2_USE_GOOGLE_GLOG', '-DCAFFE2_RNN_NO_TEXT_FORMAT', '-DGEMMLOWP_ALLOW_SLOW_SCALAR_FALLBACK=1', '-DCAFFE2_IS_XPLAT_BUILD', '-DSTRIP_ERROR_MESSAGES', '-DUSE_INTERNAL_PTHREADPOOL_IMPL', '-DCAFFE2_USE_HPTT'],
    preferred_linkage = "static",
    platform_preprocessor_flags = [['windows', ['-D_WINDOWS', '-D_WIN32', '-DWIN32', '-DNOMINMAX', '-D_CRT_SECURE_NO_WARNINGS', '-D_USE_MATH_DEFINES']], ['windows.*64$', ['-D_WIN64']]],
    preprocessor_flags = ['-DUSE_INTERNAL_PTHREADPOOL_IMPL'],
    visibility = ['PUBLIC'],
)

cxx_library(
    name = "caffe2_serialize",
    srcs = [
        "caffe2/serialize/file_adapter.cc",
        "caffe2/serialize/inline_container.cc",
        "caffe2/serialize/istream_adapter.cc",
        "caffe2/serialize/read_adapter_interface.cc",
    ],
    visibility = ["PUBLIC"],
    deps = [
        ":caffe2_headers",
        "//third_party:glog",
        "//c10:c10",
        "//third_party:miniz",
    ],
)

pt_operator_library(
    name = "torch_mobile_ops_full_dev",
    include_all_operators = True,
)

cxx_library(
    name = "pt_ops_full",
    **get_pt_operator_registry_dict(
        name = "pt_ops_full",
        deps = [
            ":torch_mobile_ops_full_dev",
        ],
    )
)

cxx_binary(
    name = 'ptmobile_benchmark',
    srcs = [
        'binaries/speed_benchmark_torch.cc',
    ],
    compiler_flags = [
        "-fexceptions",
        "-frtti",
        "-Wno-deprecated-declarations",
    ],
    preprocessor_flags = [
        "-DBUILD_LITE_INTERPRETER",
    ],
    platform_linker_flags = [
        (
            "^linux.*$",
            [
                "-Wl,--no-as-needed",
                "-ldl",
                "-pthread",
            ],
        ),
    ],
    deps = [
        ":torch_mobile_core",
        ":pt_ops_full",
        "//c10:c10",
    ],
)
