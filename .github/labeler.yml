"module: dynamo":
- torch/_dynamo/**
- torch/csrc/dynamo/**
- benchmarks/dynamo/**
- test/dynamo/**

"module: inductor":
- torch/_inductor/**
- test/inductor/**

"ciflow/inductor":
- torch/_dynamo/**
- torch/_inductor/**
- benchmarks/dynamo/**
- torch/_subclasses/fake_tensor.py
- torch/_subclasses/fake_utils.py
- torch/_subclasses/meta_utils.py
- test/distributed/test_dynamo_distributed.py
- functorch/_src/partitioners.py
- functorch/_src/aot_autograd.py

"module: cpu":
- aten/src/ATen/cpu/**
- aten/src/ATen/native/cpu/**
- aten/src/ATen/native/quantized/cpu/**
- aten/src/ATen/native/Convolution*.cpp
- aten/src/ATen/native/mkldnn/**
- torch/cpu/**
- torch/utils/mkldnn.py
- test/test_mkldnn.py

"module: mkldnn":
- third_party/ideep
- caffe2/ideep/**
- caffe2/python/ideep/**
- cmake/Modules/FindMKLDNN.cmake
- third_party/mkl-dnn.BUILD
- torch/csrc/jit/codegen/onednn/**
- test/test_jit_llga_fuser.py

"module: amp (automated mixed precision)":
- torch/amp/**
- aten/src/ATen/autocast_mode.*
- torch/csrc/jit/passes/autocast.cpp
- test/test_autocast.py

"NNC":
- torch/csrc/jit/tensorexpr/**

"release notes: quantization":
- torch/ao/quantization/**
- torch/quantization/**
- aten/src/ATen/quantized/**
- aten/src/ATen/native/quantized/cpu/**
- test/quantization/**
