- name: ONNX exporter
  patterns:
  - .ci/caffe2/*
  - .ci/onnx/*
  - aten/src/ATen/core/interned_strings.h
  - docs/source/onnx.rst
  - docs/source/onnx*
  - docs/source/scripts/onnx/**
  - scripts/onnx/**
  - test/onnx/**
  - tools/onnx/**
  - torch/_C/__init__.pyi.in
  - torch/csrc/jit/passes/onnx.*
  - torch/csrc/jit/passes/onnx/**
  - torch/csrc/jit/serialization/export.*
  - torch/csrc/jit/serialization/onnx.*
  - torch/csrc/onnx/**
  - torch/onnx/**
  - third_party/onnx
  - caffe2/python/onnx/**
  approved_by:
  - Bowen<PERSON><PERSON>
  - abock
  - justinchuby
  - shubhambhokare1
  - thiagocrepaldi
  - titaiwangms
  - wschin
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: NVFuser
  patterns:
  - test/test_jit_cuda_fuser.py
  - torch/csrc/jit/codegen/fuser/cuda/**
  - torch/csrc/jit/codegen/cuda/**
  - benchmarks/cpp/nvfuser/**
  approved_by:
  - csarofeen
  - ngimel
  - jjsjann123
  - kevinstephano
  - ptrblck
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: OSS CI
  patterns:
  - .github/**
  - .circleci/**
  - .ci/**
  - scripts/**
  - tools/**
  approved_by:
  - alband
  - dagitses
  - pytorch/pytorch-dev-infra
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: OSS CI / pytorchbot
  patterns:
  - .github/ci_commit_pins/vision.txt
  - .github/ci_commit_pins/torchdynamo.txt
  approved_by:
  - pytorchbot
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: OSS CI / pytorchbot / XLA
  patterns:
  - .github/ci_commit_pins/xla.txt
  approved_by:
  - pytorchbot
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull / linux-bionic-py3_7-clang8-xla / build
  - pull / linux-bionic-py3_7-clang8-xla / test (xla, 1, 1, linux.2xlarge)

- name: Documentation
  patterns:
  - docs/**
  - torch/*docs.py
  approved_by:
  - svekars
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: Mobile
  patterns:
  - ios/**
  - android/**
  - test/mobile/**
  approved_by:
  - linbinyu
  - IvanKobzarev
  - dreiss
  - raziel
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: Linear Algebra
  patterns:
  - aten/src/ATen/native/cuda/linalg/**
  - aten/src/ATen/LinalgBackend.h
  - aten/src/ATen/native/**LinearAlgebra*
  - docs/source/linalg.rst
  - torch/linalg/**
  - torch/_linalg_utils.py
  - torch/**python_linalg_functions.*
  - torch/**linalg.h
  - tools/autograd/templates/python_linalg_functions.cpp
  - test/test_linalg.py
  approved_by:
  - mruberry
  - lezcano
  - IvanYashchuk
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: FFT
  patterns:
  - aten/src/ATen/native/cuda/*FFT*.h
  - aten/src/ATen/native/SpectralOps.cpp
  - aten/src/ATen/native/mkl/SpectralOps.cpp
  - aten/src/ATen/native/cuda/SpectralOps.*
  - docs/source/fft.rst
  - torch/fft/**
  - torch/csrc/api/include/torch/fft.h
  - torch/**python_fft_functions.*
  - tools/autograd/templates/python_fft_functions.cpp
  - test/cpp/api/fft.cpp
  approved_by:
  - mruberry
  - peterbell10
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: Sparse
  patterns:
  - benchmarks/sparse
  - c10/util/sparse_bitset.h
  - docs/source/sparse.rst
  - torch/**sparse/**
  - torch/**sparse*
  - torch/optim/sparse*
  - torch/ao/nn/sparse/**
  - torch/utils/benchmark/**sparse*
  - aten/src/ATen/native/ao_sparse/**
  - aten/src/ATen/native/sparse/**
  - aten/src/ATen/**Sparse*
  - aten/src/ATen/*Sparse*
  - torch/_masked/**
  - test/*_masked*
  - test/**sparse*
  approved_by:
  - nikitaved
  - cpuhrsch
  - pearu
  - IvanYashchuk
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: MPS
  patterns:
  - test/test_mps.py
  - aten/src/ATen/native/native_functions.yaml
  - aten/src/ATen/mps/**
  - aten/src/ATen/native/mps/**
  approved_by:
  - kulinseth
  - alband
  - malfet
  - razarmehr
  - DenisVieriu97
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull
- name: Distributions
  patterns:
  - torch/distributions/**
  - test/distributions/**
  approved_by:
  - fritzo
  - neerajprad
  - alicanb
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: Distributed
  patterns:
  - docs/source/pipeline.rst
  - docs/source/distributed*
  - docs/source/rpc.rst
  - docs/source/rpc/**
  - docs/source/_static/img/rpc*
  - docs/source/_static/img/*distributed*
  - docs/source/elastic/**
  - benchmarks/distributed/**
  - torch/distributed/**
  - torch/nn/parallel/distributed*
  - torch/_C/_distributed*
  - torch/csrc/distributed/**
  - torch/testing/_internal/distributed/**
  - test/distributed/**
  - test/cpp/dist_autograd/**
  - test/cpp/rpc/**
  approved_by:
  - mrshenli
  - pritamdamania87
  - zhaojuanmao
  - rohan-varma
  - wanchaol
  - fduwjj
  - H-Huang
  - aazzolini
  - kwen2501
  - XilunWu
  - wz337
  - awgu
  - fegin
  - kumpera
  - yhcharles
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: IDEEP
  patterns:
  - third_party/ideep
  - caffe2/ideep/**
  - caffe2/python/ideep/**
  - cmake/Modules/FindMKLDNN.cmake
  - third_party/mkl-dnn.BUILD
  approved_by:
  - XiaobingSuper
  - jgong5
  - mingfeima
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: oneDNN graph
  patterns:
  - torch/csrc/jit/codegen/onednn/**
  - test/test_jit_llga_fuser.py
  approved_by:
  - sanchitintel
  - chunyuan-w
  - jgong5
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: CPU ATen backend
  patterns:
  - aten/src/ATen/cpu/**
  - aten/src/ATen/native/cpu/**
  - aten/src/ATen/native/quantized/cpu/**
  - aten/src/ATen/native/Convolution*.cpp
  - aten/src/ATen/native/mkldnn/**
  - test/test_mkldnn.py
  approved_by:
  - mingfeima
  - XiaobingSuper
  - jgong5
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: CPU frontend
  patterns:
  - torch/cpu/**
  - torch/utils/mkldnn.py
  - test/test_mkldnn.py
  approved_by:
  - leslie-fang-intel
  - jgong5
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: Autocast
  patterns:
  - torch/amp/**
  - aten/src/ATen/autocast_mode.*
  - torch/csrc/jit/passes/autocast.cpp
  - test/test_autocast.py
  approved_by:
  - leslie-fang-intel
  - jgong5
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: NNC
  patterns:
  - torch/csrc/jit/tensorexpr/**
  approved_by:
  - EikanWang
  - jgong5
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: Lazy Tensor
  patterns:
  - torch/csrc/lazy/**
  - test/cpp/lazy/**
  - test/lazy/**
  - torchgen/api/lazy.py
  - torchgen/dest/lazy_ir.py
  - torchgen/dest/lazy_ts_lowering.py
  - torchgen/gen_lazy_tensor.py
  - aten/src/ATen/native/ts_native_functions.yaml
  - .github/ci_commit_pins/xla.txt
  approved_by:
  - alanwaketan
  - JackCaoG
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: ROCm
  patterns:
  - '**rocm**'
  - '**hip**'
  approved_by:
  - jeffdaily
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: superuser
  patterns:
  - '*'
  approved_by:
  - pytorch/metamates
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: Core Reviewers
  patterns:
  - '*'
  approved_by:
  - mruberry
  - lezcano
  - Skylion007
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull

- name: Core Maintainers
  patterns:
  - '*'
  approved_by:
  - soumith
  - gchanan
  - ezyang
  - dzhulgakov
  mandatory_checks_name:
  - EasyCLA
  - Lint
  - pull
