name: Setup Linux

description: Set up Docker workspace on EC2

runs:
  using: composite
  steps:
    - name: Display EC2 information
      shell: bash
      run: |
        set -euo pipefail
        function get_ec2_metadata() {
          # Pulled from instance metadata endpoint for EC2
          # see https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/instancedata-data-retrieval.html
          category=$1
          curl -fsSL "http://***************/latest/meta-data/${category}"
        }
        echo "ami-id: $(get_ec2_metadata ami-id)"
        echo "instance-id: $(get_ec2_metadata instance-id)"
        echo "instance-type: $(get_ec2_metadata instance-type)"
        echo "system info $(uname -a)"

    - name: Start docker if docker deamon is not running
      shell: bash
      run: |
        if systemctl is-active --quiet docker; then
            echo "Docker daemon is running...";
        else
            echo "Starting docker deamon..." && sudo systemctl start docker;
        fi

    - name: Log in to ECR
      shell: bash
      env:
        AWS_RETRY_MODE: standard
        AWS_MAX_ATTEMPTS: "5"
        AWS_DEFAULT_REGION: us-east-1
      run: |
        AWS_ACCOUNT_ID=$(aws sts get-caller-identity|grep Account|cut -f4 -d\")
        retry () { "$@"  || (sleep 1 && "$@") || (sleep 2 && "$@") }
        retry aws ecr get-login-password --region "$AWS_DEFAULT_REGION" | docker login --username AWS \
            --password-stdin "$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com"

    - name: Preserve github env variables for use in docker
      shell: bash
      run: |
        env | grep '^GITHUB' >> "/tmp/github_env_${GITHUB_RUN_ID}"
        env | grep '^CI' >> "/tmp/github_env_${GITHUB_RUN_ID}"
