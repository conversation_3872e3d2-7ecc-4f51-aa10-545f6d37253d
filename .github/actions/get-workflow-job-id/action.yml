name: Get workflow job id

description: Get the ID of the workflow job that is currently running.

inputs:
  github-token:
    description: GITHUB_TOKEN
    required: true

outputs:
  job-id:
    description: The retrieved workflow job id
    value: ${{ steps.get-job-id.outputs.job-id }}

runs:
  using: composite
  steps:
    - name: Get jobid or fail
      # timeout-minutes is unsupported for composite workflows, see https://github.com/actions/runner/issues/1979
      # timeout-minutes: 10
      shell: bash
      id: get-job-id
      run: |
        set -eux
        GHA_WORKFLOW_JOB_ID=$(python3 .github/scripts/get_workflow_job_id.py "${GITHUB_RUN_ID}" "${RUNNER_NAME}")
        echo "job-id=${GHA_WORKFLOW_JOB_ID}" >> "${GITHUB_OUTPUT}"
      env:
        GITHUB_TOKEN: ${{ inputs.github-token }}
