name: Filter test configs matrix

description: |
  Apply filter to the test configs matrix to keep only entries specified
  by the PR test-config labels. If no test-config label is set, the same
  test configs matrix is returned untouched.

inputs:
  github-token:
    description: GITHUB_TOKEN
    required: true
  test-matrix:
    required: true
    type: string
    description: JSON description of what test configs to run.

outputs:
  test-matrix:
    description: The filtered test configs matrix.
    value: ${{ steps.filter.outputs.test-matrix }}
  is-test-matrix-empty:
    description: True if the filtered test configs matrix is empty. False otherwise.
    value: ${{ steps.filter.outputs.is-test-matrix-empty }}
  keep-going:
    description: True if keep-going label was on PR.
    value: ${{ steps.filter.outputs.keep-going }}

runs:
  using: composite
  steps:
    - uses: nick-fields/retry@3e91a01664abd3c5cd539100d10d33b9c5b68482
      name: Setup dependencies
      env:
        GITHUB_TOKEN: ${{ inputs.github-token }}
      with:
        shell: bash
        timeout_minutes: 10
        max_attempts: 5
        retry_wait_seconds: 30
        command: |
          set -eux
          python3 -m pip install requests==2.26.0 pyyaml==6.0

    - name: Parse ref
      shell: bash
      id: parse-ref
      run: .github/scripts/parse_ref.py

    - name: Select all requested test configurations
      shell: bash
      env:
        GITHUB_TOKEN: ${{ inputs.github-token }}
      id: filter
      run: |
        .github/scripts/filter_test_configs.py \
          --test-matrix "${{ inputs.test-matrix }}" \
          --pr-number "${{ github.event.pull_request.number }}" \
          --tag "${{ steps.parse-ref.outputs.tag }}" \
          --event-name "${{ github.event_name }}" \
          --schedule "${{ github.event.schedule }}"

    - name: Print the filtered test matrix
      shell: bash
      run: |
        echo "${{ steps.filter.outputs.test-matrix }}"
