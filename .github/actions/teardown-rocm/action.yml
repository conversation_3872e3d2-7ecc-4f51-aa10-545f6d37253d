name: Teardown ROCm host

description: Tear down ROCm host for CI

runs:
  using: composite
  steps:
    - name: Teardown ROCm
      if: always()
      shell: bash
      run: |
        # ignore expansion of "docker ps -q" since it could be empty
        # shellcheck disable=SC2046
        docker stop $(docker ps -q) || true
        # Prune all stopped containers.
        docker container prune -f
    - name: Runner diskspace health check
      uses: ./.github/actions/diskspace-cleanup
      if: always()
