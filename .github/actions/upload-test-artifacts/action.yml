name: Upload test artifacts

description: Upload various artifacts produced by our testing process

inputs:
  use-gha:
    description: If set to any value, upload GHA. Otherwise upload to S3.
    required: false
  file-suffix:
    description: |
      Suffix to add to the filename of the artifacts. This should include the
      workflow job id, see [Job id in artifacts].
    required: true

runs:
  using: composite
  steps:
    # Mac/Linux zip
    - name: Zip JSONs for upload
      if: runner.os != 'Windows' && !inputs.use-gha
      shell: bash
      env:
        FILE_SUFFIX: ${{ inputs.file-suffix }}
      run: |
        # Remove any previous test jsons if they exist
        rm -f test-jsons-*.zip
        zip -r "test-jsons-${FILE_SUFFIX}.zip" test -i '*.json'

    - name: Zip test reports for upload
      if: runner.os != 'Windows' && !inputs.use-gha
      shell: bash
      env:
        FILE_SUFFIX: ${{ inputs.file-suffix }}
      run: |
        # Remove any previous test reports if they exist
        rm -f test-reports-*.zip
        zip -r "test-reports-${FILE_SUFFIX}.zip" test -i '*.xml' -i '*.csv'

    - name: Zip usage log for upload
      if: runner.os != 'Windows' && !inputs.use-gha
      shell: bash
      env:
        FILE_SUFFIX: ${{ inputs.file-suffix }}
      run: |
        # Remove any previous test reports if they exist
        rm -f usage-log-*.zip
        # this workflow is also run in bazel build test, but we dont generate usage reports for it
        # so check to see if the file exists first
        if [ -f 'usage_log.txt' ]; then
            zip "usage-log-${FILE_SUFFIX}.zip" 'usage_log.txt'
        fi
        if ls test/**/*.log 1> /dev/null 2>&1; then
            zip -r "usage-log-${FILE_SUFFIX}.zip" test -i '*.log'
        fi

    # Windows zip
    - name: Zip JSONs for upload
      if: runner.os == 'Windows' && !inputs.use-gha
      shell: powershell
      env:
        FILE_SUFFIX: ${{ inputs.file-suffix }}
      run: |
        # -ir => recursive include all files in pattern
        7z a "test-jsons-$Env:FILE_SUFFIX.zip" -ir'!test\*.json'

    - name: Zip test reports for upload
      if: runner.os == 'Windows' && !inputs.use-gha
      shell: powershell
      env:
        FILE_SUFFIX: ${{ inputs.file-suffix }}
      run: |
        # -ir => recursive include all files in pattern
        7z a "test-reports-$Env:FILE_SUFFIX.zip" -ir'!test\*.xml' -ir'!test\*.csv'

    - name: Zip usage log for upload
      if: runner.os == 'Windows' && !inputs.use-gha
      continue-on-error: true
      shell: powershell
      env:
        FILE_SUFFIX: ${{ inputs.file-suffix }}
      run: |
        # -ir => recursive include all files in pattern
        7z a "usage-log-$Env:FILE_SUFFIX.zip" 'usage_log.txt' -ir'!test\*.log'

    # S3 upload
    - name: Store Test Downloaded JSONs on S3
      uses: seemethere/upload-artifact-s3@v5
      if: ${{ !inputs.use-gha }}
      with:
        s3-prefix: |
          ${{ github.repository }}/${{ github.run_id }}/${{ github.run_attempt }}/artifact
        retention-days: 14
        if-no-files-found: warn
        path: test-jsons-*.zip

    - name: Store Test Reports on S3
      uses: seemethere/upload-artifact-s3@v5
      if: ${{ !inputs.use-gha }}
      with:
        s3-prefix: |
          ${{ github.repository }}/${{ github.run_id }}/${{ github.run_attempt }}/artifact
        retention-days: 14
        if-no-files-found: error
        path: test-reports-*.zip

    - name: Store Usage Logs on S3
      uses: seemethere/upload-artifact-s3@v5
      if: ${{ !inputs.use-gha }}
      continue-on-error: true
      with:
        s3-prefix: |
          ${{ github.repository }}/${{ github.run_id }}/${{ github.run_attempt }}/artifact
        retention-days: 14
        if-no-files-found: ignore
        path: usage-log-*.zip

    # GHA upload
    - name: Store Test Downloaded JSONs on Github
      uses: actions/upload-artifact@v3
      if: inputs.use-gha
      continue-on-error: true
      with:
        # Add the run attempt, see [Artifact run attempt]
        name: test-jsons-runattempt${{ github.run_attempt }}-${{ inputs.file-suffix }}.zip
        retention-days: 14
        if-no-files-found: warn
        path: test/**/*.json

    - name: Store Test Reports on Github
      uses: actions/upload-artifact@v3
      if: inputs.use-gha
      continue-on-error: true
      with:
        # Add the run attempt, see [Artifact run attempt]
        name: test-reports-runattempt${{ github.run_attempt }}-${{ inputs.file-suffix }}.zip
        retention-days: 14
        # Don't want to fail the workflow here because not all workflows have csv files
        if-no-files-found: ignore
        path: |
          test/**/*.xml
          test/**/*.csv

    - name: Store Usage Logs on Github
      uses: actions/upload-artifact@v3
      if: inputs.use-gha
      continue-on-error: true
      with:
        # Add the run attempt, see [Artifact run attempt]
        name: usage-log-runattempt${{ github.run_attempt }}-${{ inputs.file-suffix }}.zip
        retention-days: 14
        if-no-files-found: ignore
        path: |
          usage_log.txt
          test/**/*.log
