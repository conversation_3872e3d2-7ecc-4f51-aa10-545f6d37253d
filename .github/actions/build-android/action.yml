name: build android

description: build android for a specific arch

inputs:
  arch:
    description: arch to build
    required: true
  arch-for-build-env:
    description: |
      arch to pass to build environment.
      This is currently different than the arch name we use elswhere, which
      should be fixed.
    required: true
  github-secret:
    description: github token
    required: true
  build-environment:
    required: true
    description: Top-level label for what's being built/tested.
  docker-image:
    required: true
    description: Name of the base docker image to build with.
  branch:
    required: true
    description: What branch we are building on.
outputs:
  container_id:
    description: Docker container identifier used to build the artifacts
    value: ${{ steps.build.outputs.container_id }}

runs:
  using: composite
  steps:
    - name: Build-${{ inputs.arch }}
      id: build
      shell: bash
      env:
        BRANCH: ${{ inputs.branch }}
        BUILD_ENVIRONMENT: pytorch-linux-xenial-py3-clang5-android-ndk-r19c-${{ inputs.arch-for-build-env }}-build"
        AWS_DEFAULT_REGION: us-east-1
        PR_NUMBER: ${{ github.event.pull_request.number }}
        SHA1: ${{ github.event.pull_request.head.sha || github.sha }}
        SCCACHE_BUCKET: ossci-compiler-cache-circleci-v2
        DOCKER_IMAGE: ${{ inputs.docker-image  }}
        MATRIX_ARCH: ${{ inputs.arch }}
      run: |
        # detached container should get cleaned up by teardown_ec2_linux
        set -exo pipefail
        export container_name
        container_name=$(docker run \
          -e BUILD_ENVIRONMENT \
          -e MAX_JOBS="$(nproc --ignore=2)" \
          -e AWS_DEFAULT_REGION \
          -e PR_NUMBER \
          -e SHA1 \
          -e BRANCH \
          -e SCCACHE_BUCKET \
          -e SKIP_SCCACHE_INITIALIZATION=1 \
          --env-file="/tmp/github_env_${GITHUB_RUN_ID}" \
          --security-opt seccomp=unconfined \
          --cap-add=SYS_PTRACE \
          --tty \
          --detach \
          --user jenkins \
          -w /var/lib/jenkins/workspace \
          "${DOCKER_IMAGE}"
        )
        git submodule sync && git submodule update -q --init --recursive --depth 1
        docker cp "${GITHUB_WORKSPACE}/." "${container_name}:/var/lib/jenkins/workspace"
        (echo "sudo chown -R jenkins . && .ci/pytorch/build.sh && find ${BUILD_ROOT} -type f -name "*.a" -or -name "*.o" -delete" | docker exec -u jenkins -i "${container_name}" bash) 2>&1

        # Copy install binaries back
        mkdir -p "${GITHUB_WORKSPACE}/build_android_install_${MATRIX_ARCH}"
        docker cp "${container_name}:/var/lib/jenkins/workspace/build_android/install" "${GITHUB_WORKSPACE}/build_android_install_${MATRIX_ARCH}"
        echo "container_id=${container_name}" >> "${GITHUB_OUTPUT}"
