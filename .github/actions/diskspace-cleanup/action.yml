name: Cleans up diskspace

description: Cleans up diskspace if the root directory has used more than seventy percent of your diskspace.

inputs:
    diskspace-cutoff:
        description: The percent amount after which docker prune is run.
        required: true
        default: 70

runs:
  using: composite
  steps:
    - name: Cleans up diskspace
      shell: bash
      run: |
        diskspace_cutoff=${{ inputs.diskspace-cutoff }}
        diskspace=$(df -H / --output=pcent | sed -n 2p | sed 's/%//' | sed 's/ //')
        msg="Please file an issue on pytorch/pytorch reporting the faulty runner. Include a link to the runner logs so the runner can be identified"
        if [[ "$diskspace" -ge "$diskspace_cutoff" ]] ; then
            docker system prune -af
            diskspace_new=$(df -H / --output=pcent | sed -n 2p | sed 's/%//' | sed 's/ //')
            if [[ "$diskspace_new" -gt "$diskspace_cutoff" ]] ; then
                echo "Error: Available diskspace is less than $diskspace_cutoff percent. Not enough diskspace."
                echo "$msg"
                exit 1
            else
                difference=$((diskspace - diskspace_new))
                echo "Diskspace saved: $difference percent"
            fi
        fi
