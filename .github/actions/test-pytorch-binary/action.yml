name: Test pytorch binary

description: Pulls the docker image and tests the pytorch binary using it. All env variable referenced in the "Test PyTorch binary" step must be set in the GITHUB_ENV file

runs:
  using: composite
  steps:
    - name: Test PyTorch binary
      shell: bash
      run: |
        set -x
        # shellcheck disable=SC2086,SC2090
        container_name=$(docker run \
          ${GPU_FLAG:-} \
          -e BINARY_ENV_FILE \
          -e BUILDER_ROOT \
          -e BUILD_ENVIRONMENT \
          -e DESIRED_CUDA \
          -e DESIRED_DEVTOOLSET \
          -e DESIRED_PYTHON \
          -e GITHUB_ACTIONS \
          -e GPU_ARCH_TYPE \
          -e GPU_ARCH_VERSION \
          -e LIBTORCH_VARIANT \
          -e PACKAGE_TYPE \
          -e PYTORCH_FINAL_PACKAGE_DIR \
          -e PYTORCH_ROOT \
          -e SKIP_ALL_TESTS \
          --tty \
          --detach \
          -v "${GITHUB_WORKSPACE}/pytorch:/pytorch" \
          -v "${GITHUB_WORKSPACE}/builder:/builder" \
          -v "${RUNNER_TEMP}/artifacts:/final_pkgs" \
          -w / \
          "${DOCKER_IMAGE}"
        )
        docker exec -t -w "${PYTORCH_ROOT}" "${container_name}" bash -c "bash .circleci/scripts/binary_populate_env.sh"
        # Generate test script
        docker exec -t -w "${PYTORCH_ROOT}" -e OUTPUT_SCRIPT="/run.sh" "${container_name}" bash -c "bash .circleci/scripts/binary_linux_test.sh"
        docker exec -t "${container_name}" bash -c "source ${BINARY_ENV_FILE} && bash -x /run.sh"
