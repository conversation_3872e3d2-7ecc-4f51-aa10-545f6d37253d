---
name: "⚠️ CI SEV"
about: Tracking incidents for PyTorch's CI infra.
---

> NOTE: Remember to label this issue with "`ci: sev`"

**MERGE BLOCKING** <!-- remove this line if you don't want this SEV to block merges -->

## Current Status
*Status could be: preemptive, ongoing, mitigated, closed. Also tell people if they need to take action to fix it (i.e. rebase)*.

## Error looks like
*Provide some way users can tell that this SEV is causing their issue.*

## Incident timeline (all times pacific)
*Include when the incident began, when it was detected, mitigated, root caused, and finally closed.*

<details>
<summary> Click for example </summary>

e.g.
- 10/30 7:27a incident began
- 10/30 8:30a detected by <method>
- 10/30 9:00 pm root caused as…
- 10/30 9:10 pm mitigated by…
- 10/31 10: am closed by…

</details>

## User impact
*How does this affect users of PyTorch CI?*

## Root cause
*What was the root cause of this issue?*

## Mitigation
*How did we mitigate the issue?*

## Prevention/followups
*How do we prevent issues like this in the future?*
