# Documented at https://github.com/necojackarc/auto-request-review
reviewers:
  groups:
    symbolic-shapes:
      - ezyang
      - Chillee
      - albanD
      - miladm
      - bdhirsh
      - voznese<PERSON><PERSON>
      - jbschlosser

  per_author:
    symbolic-shapes:
      - symbolic-shapes
      - antoniojkim
      - wconstab
      - SherlockNoMad

files:
  # none yet, TODO: migrate CODEOWNERS here

options:
  ignore_draft: true
  ignored_keywords:
    - DO NOT REVIEW
  # Just manually setup a self-referential per_author rule if you
  # want group assignment
  enable_group_assignment: false
