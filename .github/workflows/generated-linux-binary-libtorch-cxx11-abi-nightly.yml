# @generated DO NOT EDIT MANUALLY

# Template is at:    .github/templates/linux_binary_build_workflow.yml.j2
# Generation script: .github/scripts/generate_ci_workflows.py
name: linux-binary-libtorch-cxx11-abi

on:
  push:
    # NOTE: Meta Employees can trigger new nightlies using: https://fburl.com/trigger_pytorch_nightly_build
    branches:
      - nightly
    tags:
      # NOTE: Binary build pipelines should only get triggered on release candidate builds
      # Release candidate tags look like: v1.11.0-rc1
      - v[0-9]+.[0-9]+.[0-9]+-rc[0-9]+
      - 'ciflow/binaries/*'
      - 'ciflow/binaries_libtorch/*'
  workflow_dispatch:

env:
  # Needed for conda builds
  ALPINE_IMAGE: "308535385114.dkr.ecr.us-east-1.amazonaws.com/tool/alpine"
  ANACONDA_USER: pytorch
  AWS_DEFAULT_REGION: us-east-1
  BINARY_ENV_FILE: /tmp/env
  BUILD_ENVIRONMENT: linux-binary-libtorch-cxx11-abi
  BUILDER_ROOT: /builder
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  PR_NUMBER: ${{ github.event.pull_request.number }}
  PYTORCH_FINAL_PACKAGE_DIR: /artifacts
  PYTORCH_ROOT: /pytorch
  SHA1: ${{ github.event.pull_request.head.sha || github.sha }}
  SKIP_ALL_TESTS: 1
concurrency:
  group: linux-binary-libtorch-cxx11-abi-${{ github.event.pull_request.number || github.ref_name }}-${{ github.ref_type == 'branch' && github.sha }}-${{ github.event_name == 'workflow_dispatch' }}
  cancel-in-progress: true

jobs:
  libtorch-cpu-shared-with-deps-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cpu
      LIBTORCH_VARIANT: shared-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cpu-shared-with-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  libtorch-cpu-shared-with-deps-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cpu-shared-with-deps-cxx11-abi-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cpu
      LIBTORCH_VARIANT: shared-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cpu-shared-with-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
      runs_on: linux.4xlarge
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  libtorch-cpu-shared-with-deps-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cpu-shared-with-deps-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cpu
      LIBTORCH_VARIANT: shared-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cpu-shared-with-deps-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  libtorch-cpu-shared-without-deps-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cpu
      LIBTORCH_VARIANT: shared-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cpu-shared-without-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  libtorch-cpu-shared-without-deps-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cpu-shared-without-deps-cxx11-abi-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cpu
      LIBTORCH_VARIANT: shared-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cpu-shared-without-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
      runs_on: linux.4xlarge
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  libtorch-cpu-shared-without-deps-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cpu-shared-without-deps-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cpu
      LIBTORCH_VARIANT: shared-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cpu-shared-without-deps-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  libtorch-cpu-static-with-deps-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cpu
      LIBTORCH_VARIANT: static-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cpu-static-with-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  libtorch-cpu-static-with-deps-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cpu-static-with-deps-cxx11-abi-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cpu
      LIBTORCH_VARIANT: static-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cpu-static-with-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
      runs_on: linux.4xlarge
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  libtorch-cpu-static-with-deps-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cpu-static-with-deps-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cpu
      LIBTORCH_VARIANT: static-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cpu-static-with-deps-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  libtorch-cpu-static-without-deps-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cpu
      LIBTORCH_VARIANT: static-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cpu-static-without-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  libtorch-cpu-static-without-deps-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cpu-static-without-deps-cxx11-abi-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cpu
      LIBTORCH_VARIANT: static-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cpu-static-without-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
      runs_on: linux.4xlarge
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  libtorch-cpu-static-without-deps-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cpu-static-without-deps-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cpu
      LIBTORCH_VARIANT: static-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cpu-static-without-deps-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  libtorch-cuda11_7-shared-with-deps-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.7
      LIBTORCH_VARIANT: shared-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_7-shared-with-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  libtorch-cuda11_7-shared-with-deps-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cuda11_7-shared-with-deps-cxx11-abi-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.7
      LIBTORCH_VARIANT: shared-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_7-shared-with-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  libtorch-cuda11_7-shared-with-deps-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cuda11_7-shared-with-deps-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.7
      LIBTORCH_VARIANT: shared-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_7-shared-with-deps-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  libtorch-cuda11_7-shared-without-deps-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.7
      LIBTORCH_VARIANT: shared-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_7-shared-without-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  libtorch-cuda11_7-shared-without-deps-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cuda11_7-shared-without-deps-cxx11-abi-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.7
      LIBTORCH_VARIANT: shared-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_7-shared-without-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  libtorch-cuda11_7-shared-without-deps-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cuda11_7-shared-without-deps-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.7
      LIBTORCH_VARIANT: shared-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_7-shared-without-deps-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  libtorch-cuda11_7-static-with-deps-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.7
      LIBTORCH_VARIANT: static-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_7-static-with-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  libtorch-cuda11_7-static-with-deps-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cuda11_7-static-with-deps-cxx11-abi-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.7
      LIBTORCH_VARIANT: static-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_7-static-with-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  libtorch-cuda11_7-static-with-deps-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cuda11_7-static-with-deps-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.7
      LIBTORCH_VARIANT: static-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_7-static-with-deps-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  libtorch-cuda11_7-static-without-deps-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.7
      LIBTORCH_VARIANT: static-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_7-static-without-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  libtorch-cuda11_7-static-without-deps-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cuda11_7-static-without-deps-cxx11-abi-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.7
      LIBTORCH_VARIANT: static-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_7-static-without-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  libtorch-cuda11_7-static-without-deps-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cuda11_7-static-without-deps-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.7
      LIBTORCH_VARIANT: static-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_7-static-without-deps-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  libtorch-cuda11_8-shared-with-deps-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.8
      LIBTORCH_VARIANT: shared-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_8-shared-with-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  libtorch-cuda11_8-shared-with-deps-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cuda11_8-shared-with-deps-cxx11-abi-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.8
      LIBTORCH_VARIANT: shared-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_8-shared-with-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  libtorch-cuda11_8-shared-with-deps-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cuda11_8-shared-with-deps-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.8
      LIBTORCH_VARIANT: shared-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_8-shared-with-deps-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  libtorch-cuda11_8-shared-without-deps-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.8
      LIBTORCH_VARIANT: shared-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_8-shared-without-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  libtorch-cuda11_8-shared-without-deps-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cuda11_8-shared-without-deps-cxx11-abi-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.8
      LIBTORCH_VARIANT: shared-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_8-shared-without-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  libtorch-cuda11_8-shared-without-deps-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cuda11_8-shared-without-deps-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.8
      LIBTORCH_VARIANT: shared-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_8-shared-without-deps-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  libtorch-cuda11_8-static-with-deps-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.8
      LIBTORCH_VARIANT: static-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_8-static-with-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  libtorch-cuda11_8-static-with-deps-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cuda11_8-static-with-deps-cxx11-abi-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.8
      LIBTORCH_VARIANT: static-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_8-static-with-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  libtorch-cuda11_8-static-with-deps-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cuda11_8-static-with-deps-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.8
      LIBTORCH_VARIANT: static-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_8-static-with-deps-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  libtorch-cuda11_8-static-without-deps-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.8
      LIBTORCH_VARIANT: static-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_8-static-without-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  libtorch-cuda11_8-static-without-deps-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cuda11_8-static-without-deps-cxx11-abi-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.8
      LIBTORCH_VARIANT: static-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_8-static-without-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  libtorch-cuda11_8-static-without-deps-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-cuda11_8-static-without-deps-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:cuda11.8
      LIBTORCH_VARIANT: static-without-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-cuda11_8-static-without-deps-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  libtorch-rocm5_3-shared-with-deps-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.3
      GPU_ARCH_VERSION: 5.3
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:rocm5.3
      LIBTORCH_VARIANT: shared-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-rocm5_3-shared-with-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  libtorch-rocm5_3-shared-with-deps-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-rocm5_3-shared-with-deps-cxx11-abi-build
    runs-on: linux.rocm.gpu
    timeout-minutes: 240
    env:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.3
      GPU_ARCH_VERSION: 5.3
      GPU_ARCH_TYPE: rocm
      SKIP_ALL_TESTS: 1
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:rocm5.3
      LIBTORCH_VARIANT: shared-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
    steps:
      - name: Setup ROCm
        uses: ./.github/actions/setup-rocm
      - uses: actions/download-artifact@v3
        name: Download Build Artifacts
        with:
          name: libtorch-rocm5_3-shared-with-deps-cxx11-abi
          path: "${{ runner.temp }}/artifacts/"
      - name: Checkout PyTorch
        uses: malfet/checkout@silent-checkout
        with:
          submodules: recursive
          path: pytorch
          quiet-checkout: true
      - name: Clean PyTorch checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: pytorch
      - name: Checkout pytorch/builder
        uses: malfet/checkout@silent-checkout
        with:
          ref: release/2.0
          submodules: recursive
          repository: pytorch/builder
          path: builder
          quiet-checkout: true
      - name: Clean pytorch/builder checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: builder
      - name: ROCm set GPU_FLAG
        run: |
          echo "GPU_FLAG=--device=/dev/mem --device=/dev/kfd --device=/dev/dri --group-add video --group-add daemon" >> "${GITHUB_ENV}"
      - name: Pull Docker image
        uses: pytorch/test-infra/.github/actions/pull-docker-image@main
        with:
          docker-image: pytorch/libtorch-cxx11-builder:rocm5.3
      - name: Test Pytorch binary
        uses: ./pytorch/.github/actions/test-pytorch-binary
      - name: Teardown ROCm
        uses: ./.github/actions/teardown-rocm
  libtorch-rocm5_3-shared-with-deps-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-rocm5_3-shared-with-deps-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.3
      GPU_ARCH_VERSION: 5.3
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:rocm5.3
      LIBTORCH_VARIANT: shared-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-rocm5_3-shared-with-deps-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  libtorch-rocm5_3-static-with-deps-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.3
      GPU_ARCH_VERSION: 5.3
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:rocm5.3
      LIBTORCH_VARIANT: static-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-rocm5_3-static-with-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  libtorch-rocm5_3-static-with-deps-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-rocm5_3-static-with-deps-cxx11-abi-build
    runs-on: linux.rocm.gpu
    timeout-minutes: 240
    env:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.3
      GPU_ARCH_VERSION: 5.3
      GPU_ARCH_TYPE: rocm
      SKIP_ALL_TESTS: 1
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:rocm5.3
      LIBTORCH_VARIANT: static-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
    steps:
      - name: Setup ROCm
        uses: ./.github/actions/setup-rocm
      - uses: actions/download-artifact@v3
        name: Download Build Artifacts
        with:
          name: libtorch-rocm5_3-static-with-deps-cxx11-abi
          path: "${{ runner.temp }}/artifacts/"
      - name: Checkout PyTorch
        uses: malfet/checkout@silent-checkout
        with:
          submodules: recursive
          path: pytorch
          quiet-checkout: true
      - name: Clean PyTorch checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: pytorch
      - name: Checkout pytorch/builder
        uses: malfet/checkout@silent-checkout
        with:
          ref: release/2.0
          submodules: recursive
          repository: pytorch/builder
          path: builder
          quiet-checkout: true
      - name: Clean pytorch/builder checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: builder
      - name: ROCm set GPU_FLAG
        run: |
          echo "GPU_FLAG=--device=/dev/mem --device=/dev/kfd --device=/dev/dri --group-add video --group-add daemon" >> "${GITHUB_ENV}"
      - name: Pull Docker image
        uses: pytorch/test-infra/.github/actions/pull-docker-image@main
        with:
          docker-image: pytorch/libtorch-cxx11-builder:rocm5.3
      - name: Test Pytorch binary
        uses: ./pytorch/.github/actions/test-pytorch-binary
      - name: Teardown ROCm
        uses: ./.github/actions/teardown-rocm
  libtorch-rocm5_3-static-with-deps-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-rocm5_3-static-with-deps-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.3
      GPU_ARCH_VERSION: 5.3
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:rocm5.3
      LIBTORCH_VARIANT: static-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-rocm5_3-static-with-deps-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  libtorch-rocm5_4_2-shared-with-deps-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.4.2
      GPU_ARCH_VERSION: 5.4.2
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:rocm5.4.2
      LIBTORCH_VARIANT: shared-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-rocm5_4_2-shared-with-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  libtorch-rocm5_4_2-shared-with-deps-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-rocm5_4_2-shared-with-deps-cxx11-abi-build
    runs-on: linux.rocm.gpu
    timeout-minutes: 240
    env:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.4.2
      GPU_ARCH_VERSION: 5.4.2
      GPU_ARCH_TYPE: rocm
      SKIP_ALL_TESTS: 1
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:rocm5.4.2
      LIBTORCH_VARIANT: shared-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
    steps:
      - name: Setup ROCm
        uses: ./.github/actions/setup-rocm
      - uses: actions/download-artifact@v3
        name: Download Build Artifacts
        with:
          name: libtorch-rocm5_4_2-shared-with-deps-cxx11-abi
          path: "${{ runner.temp }}/artifacts/"
      - name: Checkout PyTorch
        uses: malfet/checkout@silent-checkout
        with:
          submodules: recursive
          path: pytorch
          quiet-checkout: true
      - name: Clean PyTorch checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: pytorch
      - name: Checkout pytorch/builder
        uses: malfet/checkout@silent-checkout
        with:
          ref: release/2.0
          submodules: recursive
          repository: pytorch/builder
          path: builder
          quiet-checkout: true
      - name: Clean pytorch/builder checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: builder
      - name: ROCm set GPU_FLAG
        run: |
          echo "GPU_FLAG=--device=/dev/mem --device=/dev/kfd --device=/dev/dri --group-add video --group-add daemon" >> "${GITHUB_ENV}"
      - name: Pull Docker image
        uses: pytorch/test-infra/.github/actions/pull-docker-image@main
        with:
          docker-image: pytorch/libtorch-cxx11-builder:rocm5.4.2
      - name: Test Pytorch binary
        uses: ./pytorch/.github/actions/test-pytorch-binary
      - name: Teardown ROCm
        uses: ./.github/actions/teardown-rocm
  libtorch-rocm5_4_2-shared-with-deps-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-rocm5_4_2-shared-with-deps-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.4.2
      GPU_ARCH_VERSION: 5.4.2
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:rocm5.4.2
      LIBTORCH_VARIANT: shared-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-rocm5_4_2-shared-with-deps-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  libtorch-rocm5_4_2-static-with-deps-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.4.2
      GPU_ARCH_VERSION: 5.4.2
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:rocm5.4.2
      LIBTORCH_VARIANT: static-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-rocm5_4_2-static-with-deps-cxx11-abi
      build_environment: linux-binary-libtorch-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  libtorch-rocm5_4_2-static-with-deps-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-rocm5_4_2-static-with-deps-cxx11-abi-build
    runs-on: linux.rocm.gpu
    timeout-minutes: 240
    env:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.4.2
      GPU_ARCH_VERSION: 5.4.2
      GPU_ARCH_TYPE: rocm
      SKIP_ALL_TESTS: 1
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:rocm5.4.2
      LIBTORCH_VARIANT: static-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
    steps:
      - name: Setup ROCm
        uses: ./.github/actions/setup-rocm
      - uses: actions/download-artifact@v3
        name: Download Build Artifacts
        with:
          name: libtorch-rocm5_4_2-static-with-deps-cxx11-abi
          path: "${{ runner.temp }}/artifacts/"
      - name: Checkout PyTorch
        uses: malfet/checkout@silent-checkout
        with:
          submodules: recursive
          path: pytorch
          quiet-checkout: true
      - name: Clean PyTorch checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: pytorch
      - name: Checkout pytorch/builder
        uses: malfet/checkout@silent-checkout
        with:
          ref: release/2.0
          submodules: recursive
          repository: pytorch/builder
          path: builder
          quiet-checkout: true
      - name: Clean pytorch/builder checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: builder
      - name: ROCm set GPU_FLAG
        run: |
          echo "GPU_FLAG=--device=/dev/mem --device=/dev/kfd --device=/dev/dri --group-add video --group-add daemon" >> "${GITHUB_ENV}"
      - name: Pull Docker image
        uses: pytorch/test-infra/.github/actions/pull-docker-image@main
        with:
          docker-image: pytorch/libtorch-cxx11-builder:rocm5.4.2
      - name: Test Pytorch binary
        uses: ./pytorch/.github/actions/test-pytorch-binary
      - name: Teardown ROCm
        uses: ./.github/actions/teardown-rocm
  libtorch-rocm5_4_2-static-with-deps-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: libtorch-rocm5_4_2-static-with-deps-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: libtorch
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.4.2
      GPU_ARCH_VERSION: 5.4.2
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/libtorch-cxx11-builder:rocm5.4.2
      LIBTORCH_VARIANT: static-with-deps
      DESIRED_DEVTOOLSET: cxx11-abi
      build_name: libtorch-rocm5_4_2-static-with-deps-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
