name: nightly

on:
  schedule:
    - cron: 0 0 * * *
  push:
    tags:
      # NOTE: Doc build pipelines should only get triggered on release candidate builds
      # Release candidate tags look like: v1.11.0-rc1
      - v[0-9]+.[0-9]+.[0-9]+-rc[0-9]+
      - ciflow/nightly/*
  workflow_dispatch:


concurrency:
  group: ${{ github.workflow }}--${{ github.event.pull_request.number || github.sha }}-${{ github.event_name == 'workflow_dispatch' }}
  cancel-in-progress: true

jobs:
  docs-build:
    name: docs build
    uses: ./.github/workflows/_linux-build.yml
    with:
      build-environment: linux-focal-py3.8-gcc7
      docker-image-name: pytorch-linux-focal-py3.8-gcc7

  docs-push:
    name: docs push
    uses: ./.github/workflows/_docs.yml
    needs: docs-build
    with:
      build-environment: linux-focal-py3.8-gcc7
      docker-image: ${{ needs.docs-build.outputs.docker-image }}
      push: ${{ github.event_name == 'schedule' || startsWith(github.event.ref, 'refs/tags/v') }}
      run-doxygen: true
    secrets:
      GH_PYTORCHBOT_TOKEN: ${{ secrets.GH_PYTORCHBOT_TOKEN }}

  update-vision-commit-hash:
    uses: ./.github/workflows/_update-commit-hash.yml
    if: ${{ github.event_name == 'schedule' }}
    with:
      repo-name: vision
      branch: main
    secrets:
      MERGEBOT_TOKEN: ${{ secrets.MERGEBOT_TOKEN }}
      PYTORCHBOT_TOKEN: ${{ secrets.GH_PYTORCHBOT_TOKEN }}
