name: bazel

on:
  workflow_call:
    inputs:
      build-environment:
        required: true
        type: string
        description: Top-level label for what's being built/tested.
      docker-image-name:
        required: true
        type: string
        description: Name of the base docker image to build with.
      sync-tag:
        required: false
        type: string
        default: ""
        description: |
          If this is set, our linter will use this to make sure that every other
          job with the same `sync-tag` is identical.

env:
  GIT_DEFAULT_BRANCH: ${{ github.event.repository.default_branch }}

jobs:
  build-and-test:
    # Don't run on forked repos.
    if: github.repository_owner == 'pytorch'
    runs-on: [self-hosted, linux.4xlarge]
    steps:
      - name: Setup SSH (Click me for login details)
        uses: pytorch/test-infra/.github/actions/setup-ssh@main
        with:
          github-secret: ${{ secrets.GITHUB_TOKEN }}

      # [see note: pytorch repo ref]
      - name: Checkout PyTorch
        uses: pytorch/pytorch/.github/actions/checkout-pytorch@master

      - name: Setup Linux
        uses: ./.github/actions/setup-linux

      - name: <PERSON>culate docker image
        id: calculate-docker-image
        uses: ./.github/actions/calculate-docker-image
        with:
          docker-image-name: ${{ inputs.docker-image-name }}

      - name: Pull docker image
        uses: pytorch/test-infra/.github/actions/pull-docker-image@main
        with:
          docker-image: ${{ steps.calculate-docker-image.outputs.docker-image }}

      - name: Output disk space left
        run: |
          sudo df -H

      - name: Preserve github env variables for use in docker
        run: |
          env | grep '^GITHUB' >> "/tmp/github_env_${GITHUB_RUN_ID}"
          env | grep '^CI' >> "/tmp/github_env_${GITHUB_RUN_ID}"

      - name: Parse ref
        id: parse-ref
        run: .github/scripts/parse_ref.py

      - name: Get workflow job id
        id: get-job-id
        uses: ./.github/actions/get-workflow-job-id
        if: always()
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Build
        env:
          BUILD_ENVIRONMENT: ${{ inputs.build-environment }}
          BRANCH: ${{ steps.parse-ref.outputs.branch }}
          # TODO duplicated
          AWS_DEFAULT_REGION: us-east-1
          SHA1: ${{ github.event.pull_request.head.sha || github.sha }}
          SCCACHE_BUCKET: ossci-compiler-cache-circleci-v2
          TORCH_CUDA_ARCH_LIST: 5.2
          DOCKER_IMAGE: ${{ steps.calculate-docker-image.outputs.docker-image }}
          OUR_GITHUB_JOB_ID: ${{ steps.get-job-id.outputs.job-id }}
        run: |
          # detached container should get cleaned up by teardown_ec2_linux
          container_name=$(docker run \
            -e BUILD_ENVIRONMENT \
            -e MAX_JOBS="$(nproc --ignore=2)" \
            -e SCCACHE_BUCKET \
            -e SKIP_SCCACHE_INITIALIZATION=1 \
            -e TORCH_CUDA_ARCH_LIST \
            -e OUR_GITHUB_JOB_ID \
            --env-file="/tmp/github_env_${GITHUB_RUN_ID}" \
            --security-opt seccomp=unconfined \
            --cap-add=SYS_PTRACE \
            --tty \
            --detach \
            --user jenkins \
            -v "${GITHUB_WORKSPACE}:/var/lib/jenkins/workspace" \
            -w /var/lib/jenkins/workspace \
            "${DOCKER_IMAGE}"
          )
          docker exec -t "${container_name}" sh -c 'sudo chown -R jenkins . && sudo chown -R jenkins /dev && .ci/pytorch/build.sh'

      # !{{ common_android.upload_android_binary_size("", "")}}
      - name: Test
        id: test
        # Time out the test phase after 3.5 hours
        timeout-minutes: 210
        env:
          BUILD_ENVIRONMENT: ${{ inputs.build-environment }}
          PR_NUMBER: ${{ github.event.pull_request.number }}
          BRANCH: ${{ steps.parse-ref.outputs.branch }}
          SHA1: ${{ github.event.pull_request.head.sha || github.sha }}
          PYTORCH_RETRY_TEST_CASES: 1
          PYTORCH_OVERRIDE_FLAKY_SIGNAL: 1
          PR_BODY: ${{ github.event.pull_request.body }}
          SCCACHE_BUCKET: ossci-compiler-cache-circleci-v2
          DOCKER_IMAGE: ${{ steps.calculate-docker-image.outputs.docker-image }}
        run: |
          # detached container should get cleaned up by teardown_ec2_linux
          export SHARD_NUMBER=0

          COMMIT_MESSAGES=$(git cherry -v "origin/${GIT_DEFAULT_BRANCH:-master}")

          # sanitize the input commit message and PR body here:
          #
          # trim all new lines from commit messages + PR_BODY to avoid issues with batch environment
          # variable copying. see https://github.com/pytorch/pytorch/pull/80043#issuecomment-1167796028
          COMMIT_MESSAGES="${COMMIT_MESSAGES//[$'\n\r']}"
          PR_BODY="${PR_BODY//[$'\n\r']}"

          # then trim all special characters like single and double quotes to avoid unescaped inputs to
          # wreak havoc internally
          export COMMIT_MESSAGES="${COMMIT_MESSAGES//[\'\"]}"
          export PR_BODY="${PR_BODY//[\'\"]}"

          # TODO: Stop building test binaries as part of the build phase
          # Make sure we copy test results from bazel-testlogs symlink to
          # a regular directory ./test/test-reports
          container_name=$(docker run \
            -e BUILD_ENVIRONMENT \
            -e GITHUB_ACTIONS \
            -e GIT_DEFAULT_BRANCH="$GIT_DEFAULT_BRANCH" \
            -e SHARD_NUMBER \
            -e NUM_TEST_SHARDS \
            -e MAX_JOBS="$(nproc --ignore=2)" \
            -e SCCACHE_BUCKET \
            -e PR_BODY \
            -e COMMIT_MESSAGES \
            -e PYTORCH_RETRY_TEST_CASES \
            -e PYTORCH_OVERRIDE_FLAKY_SIGNAL \
            --env-file="/tmp/github_env_${GITHUB_RUN_ID}" \
            --security-opt seccomp=unconfined \
            --cap-add=SYS_PTRACE \
            --shm-size="1g" \
            --tty \
            --detach \
            --user jenkins \
            -v "${GITHUB_WORKSPACE}:/var/lib/jenkins/workspace" \
            -w /var/lib/jenkins/workspace \
            "${DOCKER_IMAGE}"
          )
          docker exec -t "${container_name}" sh -c 'sudo chown -R jenkins . && sudo chown -R jenkins /dev && .ci/pytorch/test.sh && cp -Lr ./bazel-testlogs ./test/test-reports'

      - name: Print remaining test logs
        shell: bash
        if: always()
        run: |
          cat test/**/*.log || true

      - name: Chown workspace
        uses: ./.github/actions/chown-workspace
        if: always()

      - name: Upload test artifacts
        uses: ./.github/actions/upload-test-artifacts
        if: always() && steps.test.conclusion && steps.test.conclusion != 'skipped'
        with:
          file-suffix: bazel-${{ github.job }}_${{ steps.get-job-id.outputs.job-id }}

      - name: Teardown Linux
        uses: pytorch/test-infra/.github/actions/teardown-linux@main
        if: always()
