name: periodic

on:
  schedule:
    - cron: 45 0,4,8,12,16,20 * * *
    - cron: 29 8 * * *  # about 1:29am PDT, for mem leak check and rerun disabled tests
  push:
    tags:
      - ciflow/periodic/*
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref_name }}-${{ github.ref_type == 'branch' && github.sha }}-${{ github.event_name == 'workflow_dispatch' }}-${{ github.event_name == 'schedule' }}-${{ github.event.schedule }}
  cancel-in-progress: true

jobs:
  parallelnative-linux-focal-py3_8-gcc7-build:
    name: parallelnative-linux-focal-py3.8-gcc7
    uses: ./.github/workflows/_linux-build.yml
    with:
      build-environment: parallelnative-linux-focal-py3.8-gcc7
      docker-image-name: pytorch-linux-focal-py3.8-gcc7
      test-matrix: |
        { include: [
          { config: "default", shard: 1, num_shards: 2, runner: "linux.2xlarge" },
          { config: "default", shard: 2, num_shards: 2, runner: "linux.2xlarge" },
        ]}

  parallelnative-linux-focal-py3_8-gcc7-test:
    name: parallelnative-linux-focal-py3.8-gcc7
    uses: ./.github/workflows/_linux-test.yml
    needs: parallelnative-linux-focal-py3_8-gcc7-build
    with:
      build-environment: parallelnative-linux-focal-py3.8-gcc7
      docker-image: ${{ needs.parallelnative-linux-focal-py3_8-gcc7-build.outputs.docker-image }}
      test-matrix: ${{ needs.parallelnative-linux-focal-py3_8-gcc7-build.outputs.test-matrix }}

  linux-bionic-cuda11_7-py3-gcc7-slow-gradcheck-build:
    name: linux-bionic-cuda11.7-py3-gcc7-slow-gradcheck
    uses: ./.github/workflows/_linux-build.yml
    with:
      build-environment: linux-bionic-cuda11.7-py3-gcc7-slow-gradcheck
      docker-image-name: pytorch-linux-bionic-cuda11.7-cudnn8-py3-gcc7
      test-matrix: |
        { include: [
          { config: "default", shard: 1, num_shards: 2, runner: "linux.4xlarge.nvidia.gpu" },
          { config: "default", shard: 2, num_shards: 2, runner: "linux.4xlarge.nvidia.gpu" },
        ]}

  linux-bionic-cuda11_7-py3-gcc7-slow-gradcheck-test:
    name: linux-bionic-cuda11.7-py3-gcc7-slow-gradcheck
    uses: ./.github/workflows/_linux-test.yml
    needs: linux-bionic-cuda11_7-py3-gcc7-slow-gradcheck-build
    with:
      build-environment: linux-bionic-cuda11.7-py3-gcc7-slow-gradcheck
      docker-image: ${{ needs.linux-bionic-cuda11_7-py3-gcc7-slow-gradcheck-build.outputs.docker-image }}
      test-matrix: ${{ needs.linux-bionic-cuda11_7-py3-gcc7-slow-gradcheck-build.outputs.test-matrix }}
      timeout-minutes: 300

  linux-bionic-cuda11_7-py3_10-gcc7-periodic-dynamo-benchmarks-build:
    name: cuda11.7-py3.10-gcc7-sm86-periodic-dynamo-benchmarks
    uses: ./.github/workflows/_linux-build.yml
    with:
      build-environment: linux-bionic-cuda11.7-py3.10-gcc7-sm86
      docker-image-name: pytorch-linux-bionic-cuda11.7-cudnn8-py3-gcc7
      cuda-arch-list: '8.6'
      test-matrix: |
        { include: [
          { config: "aot_eager_all", shard: 1, num_shards: 1, runner: "linux.g5.4xlarge.nvidia.gpu" },
          # These jobs run too slowly so they must be sharded, unfortunately
          { config: "dynamic_aot_eager_torchbench", shard: 1, num_shards: 1, runner: "linux.g5.4xlarge.nvidia.gpu" },
          { config: "dynamic_aot_eager_huggingface", shard: 1, num_shards: 1, runner: "linux.g5.4xlarge.nvidia.gpu" },
          { config: "dynamic_aot_eager_timm", shard: 1, num_shards: 2, runner: "linux.g5.4xlarge.nvidia.gpu" },
          { config: "dynamic_aot_eager_timm", shard: 2, num_shards: 2, runner: "linux.g5.4xlarge.nvidia.gpu" },
        ]}

  linux-bionic-cuda11_7-py3_10-gcc7-periodic-dynamo-benchmarks-test:
    name: cuda11.7-py3.10-gcc7-sm86-periodic-dynamo-benchmarks
    uses: ./.github/workflows/_linux-test.yml
    needs: linux-bionic-cuda11_7-py3_10-gcc7-periodic-dynamo-benchmarks-build
    with:
      build-environment: linux-bionic-cuda11.7-py3.10-gcc7-sm86
      docker-image: ${{ needs.linux-bionic-cuda11_7-py3_10-gcc7-periodic-dynamo-benchmarks-build.outputs.docker-image }}
      test-matrix: ${{ needs.linux-bionic-cuda11_7-py3_10-gcc7-periodic-dynamo-benchmarks-build.outputs.test-matrix }}

  linux-focal-rocm5_4_2-py3_8-build:
    name: linux-focal-rocm5.4.2-py3.8
    uses: ./.github/workflows/_linux-build.yml
    with:
      build-environment: linux-focal-rocm5.4.2-py3.8
      docker-image-name: pytorch-linux-focal-rocm-n-py3
      test-matrix: |
        { include: [
          { config: "slow", shard: 1, num_shards: 1, runner: "linux.rocm.gpu" },
          { config: "distributed", shard: 1, num_shards: 2, runner: "linux.rocm.gpu" },
          { config: "distributed", shard: 2, num_shards: 2, runner: "linux.rocm.gpu" },
        ]}

  linux-focal-rocm5_4_2-py3_8-test:
    name: linux-focal-rocm5.4.2-py3.8
    uses: ./.github/workflows/_rocm-test.yml
    needs: linux-focal-rocm5_4_2-py3_8-build
    with:
      build-environment: linux-focal-rocm5.4.2-py3.8
      docker-image: ${{ needs.linux-focal-rocm5_4_2-py3_8-build.outputs.docker-image }}
      test-matrix: ${{ needs.linux-focal-rocm5_4_2-py3_8-build.outputs.test-matrix }}
    secrets:
      AWS_OSSCI_METRICS_V2_ACCESS_KEY_ID: ${{ secrets.AWS_OSSCI_METRICS_V2_ACCESS_KEY_ID }}
      AWS_OSSCI_METRICS_V2_SECRET_ACCESS_KEY: ${{ secrets.AWS_OSSCI_METRICS_V2_SECRET_ACCESS_KEY }}

  linux-bionic-cuda11_7-py3_9-gcc7-build:
    name: linux-bionic-cuda11.7-py3.9-gcc7
    uses: ./.github/workflows/_linux-build.yml
    with:
      build-environment: linux-bionic-cuda11.7-py3.9-gcc7
      docker-image-name: pytorch-linux-bionic-cuda11.7-cudnn8-py3-gcc7
      test-matrix: |
        { include: [
          { config: "multigpu", shard: 1, num_shards: 1, runner: "linux.16xlarge.nvidia.gpu" },
        ]}
      build-with-debug: false

  linux-bionic-cuda11_7-py3_9-gcc7-test:
    name: linux-bionic-cuda11.7-py3.9-gcc7
    uses: ./.github/workflows/_linux-test.yml
    needs: linux-bionic-cuda11_7-py3_9-gcc7-build
    with:
      build-environment: linux-bionic-cuda11.7-py3.9-gcc7
      docker-image: ${{ needs.linux-bionic-cuda11_7-py3_9-gcc7-build.outputs.docker-image }}
      test-matrix: ${{ needs.linux-bionic-cuda11_7-py3_9-gcc7-build.outputs.test-matrix }}

  linux-bionic-cuda11_7-py3_10-gcc7-debug-build:
    name: linux-bionic-cuda11.7-py3.10-gcc7-debug
    uses: ./.github/workflows/_linux-build.yml
    with:
      build-environment: linux-bionic-cuda11.7-py3.10-gcc7-debug
      docker-image-name: pytorch-linux-bionic-cuda11.7-cudnn8-py3-gcc7
      build-with-debug: true
      test-matrix: |
        { include: [
          { config: "default", shard: 1, num_shards: 4, runner: "linux.4xlarge.nvidia.gpu" },
          { config: "default", shard: 2, num_shards: 4, runner: "linux.4xlarge.nvidia.gpu" },
          { config: "default", shard: 3, num_shards: 4, runner: "linux.4xlarge.nvidia.gpu" },
          { config: "default", shard: 4, num_shards: 4, runner: "linux.4xlarge.nvidia.gpu" },
        ]}

  linux-bionic-cuda11_7-py3_10-gcc7-debug-test:
    name: linux-bionic-cuda11.7-py3.10-gcc7-debug
    uses: ./.github/workflows/_linux-test.yml
    needs: linux-bionic-cuda11_7-py3_10-gcc7-debug-build
    with:
      build-environment: linux-bionic-cuda11.7-py3.10-gcc7-debug
      docker-image: ${{ needs.linux-bionic-cuda11_7-py3_10-gcc7-debug-build.outputs.docker-image }}
      test-matrix: ${{ needs.linux-bionic-cuda11_7-py3_10-gcc7-debug-build.outputs.test-matrix }}

  linux-bionic-cuda11_8-py3_8-gcc7-debug-build:
    name: linux-bionic-cuda11.8-py3.8-gcc7-debug
    uses: ./.github/workflows/_linux-build.yml
    with:
      build-environment: linux-bionic-cuda11.8-py3.8-gcc7-debug
      docker-image-name: pytorch-linux-bionic-cuda11.8-cudnn8-py3-gcc7
      build-with-debug: true
      test-matrix: |
        { include: [
          { config: "default", shard: 1, num_shards: 4, runner: "linux.4xlarge.nvidia.gpu" },
          { config: "default", shard: 2, num_shards: 4, runner: "linux.4xlarge.nvidia.gpu" },
          { config: "default", shard: 3, num_shards: 4, runner: "linux.4xlarge.nvidia.gpu" },
          { config: "default", shard: 4, num_shards: 4, runner: "linux.4xlarge.nvidia.gpu" },
        ]}

  linux-bionic-cuda11_8-py3_8-gcc7-debug-test:
    name: linux-bionic-cuda11.8-py3.8-gcc7-debug
    uses: ./.github/workflows/_linux-test.yml
    needs: linux-bionic-cuda11_8-py3_8-gcc7-debug-build
    with:
      build-environment: linux-bionic-cuda11.8-py3.8-gcc7-debug
      docker-image: ${{ needs.linux-bionic-cuda11_8-py3_8-gcc7-debug-build.outputs.docker-image }}
      test-matrix: ${{ needs.linux-bionic-cuda11_8-py3_8-gcc7-debug-build.outputs.test-matrix }}

  libtorch-linux-bionic-cuda11_8-gcc7-build:
    name: libtorch-linux-bionic-cuda11.8-gcc7
    uses: ./.github/workflows/_linux-build.yml
    with:
      build-environment: libtorch-linux-bionic-cuda11.8-gcc7
      docker-image-name: pytorch-linux-bionic-cuda11.8-cudnn8-py3-gcc7
      build-generates-artifacts: false

  win-vs2019-cuda11_8-py3-build:
    name: win-vs2019-cuda11.8-py3
    uses: ./.github/workflows/_win-build.yml
    with:
      build-environment: win-vs2019-cuda11.8-py3
      cuda-version: "11.8"
      test-matrix: |
        { include: [
          { config: "default", shard: 1, num_shards: 3, runner: "windows.g5.4xlarge.nvidia.gpu" },
          { config: "default", shard: 2, num_shards: 3, runner: "windows.g5.4xlarge.nvidia.gpu" },
          { config: "default", shard: 3, num_shards: 3, runner: "windows.g5.4xlarge.nvidia.gpu" },
          { config: "force_on_cpu", shard: 1, num_shards: 1, runner: "windows.4xlarge" },
        ]}

  win-vs2019-cuda11_8-py3-test:
    name: win-vs2019-cuda11.8-py3
    uses: ./.github/workflows/_win-test.yml
    needs: win-vs2019-cuda11_8-py3-build
    with:
      build-environment: win-vs2019-cuda11.8-py3
      cuda-version: "11.8"
      test-matrix: ${{ needs.win-vs2019-cuda11_8-py3-build.outputs.test-matrix }}

  libtorch-linux-bionic-cuda11_7-gcc7-build:
    name: libtorch-linux-bionic-cuda11.7-gcc7
    uses: ./.github/workflows/_linux-build.yml
    with:
      build-environment: libtorch-linux-bionic-cuda11.7-gcc7
      docker-image-name: pytorch-linux-bionic-cuda11.7-cudnn8-py3-gcc7
      build-generates-artifacts: false

  win-vs2019-cuda11_7-py3-build:
    name: win-vs2019-cuda11.7-py3
    uses: ./.github/workflows/_win-build.yml
    with:
      build-environment: win-vs2019-cuda11.7-py3
      cuda-version: "11.7"
      test-matrix: |
        { include: [
          { config: "default", shard: 1, num_shards: 3, runner: "windows.g5.4xlarge.nvidia.gpu" },
          { config: "default", shard: 2, num_shards: 3, runner: "windows.g5.4xlarge.nvidia.gpu" },
          { config: "default", shard: 3, num_shards: 3, runner: "windows.g5.4xlarge.nvidia.gpu" },
          { config: "force_on_cpu", shard: 1, num_shards: 1, runner: "windows.4xlarge" },
        ]}

  win-vs2019-cuda11_7-py3-test:
    name: win-vs2019-cuda11.7-py3
    uses: ./.github/workflows/_win-test.yml
    needs: win-vs2019-cuda11_7-py3-build
    with:
      build-environment: win-vs2019-cuda11.7-py3
      cuda-version: "11.7"
      test-matrix: ${{ needs.win-vs2019-cuda11_7-py3-build.outputs.test-matrix }}

  ios-12-5-1-x86-64-coreml:
    name: ios-12-5-1-x86-64-coreml
    uses: ./.github/workflows/_ios-build-test.yml
    with:
      build-environment: ios-12-5-1-x86-64-coreml
      ios-platform: SIMULATOR
      ios-arch: x86_64

  ios-12-5-1-arm64:
    name: ios-12-5-1-arm64
    uses: ./.github/workflows/_ios-build-test.yml
    with:
      build-environment: ios-12-5-1-arm64
      ios-platform: OS
      ios-arch: arm64

  ios-12-5-1-arm64-coreml:
    name: ios-12-5-1-arm64-coreml
    uses: ./.github/workflows/_ios-build-test.yml
    with:
      build-environment: ios-12-5-1-arm64-coreml
      ios-platform: OS
      ios-arch: arm64

  ios-12-5-1-arm64-custom-ops:
    name: ios-12-5-1-arm64-custom-ops
    uses: ./.github/workflows/_ios-build-test.yml
    with:
      build-environment: ios-12-5-1-arm64-custom-ops
      ios-platform: OS
      ios-arch: arm64

  ios-12-5-1-arm64-metal:
    name: ios-12-5-1-arm64-metal
    uses: ./.github/workflows/_ios-build-test.yml
    with:
      build-environment: ios-12-5-1-arm64-metal
      ios-platform: OS
      ios-arch: arm64

  buck-build-test:
    name: buck-build-test
    uses: ./.github/workflows/_buck-build-test.yml
