name: inductor

on:
  push:
    branches:
      - master
      - main
    tags:
      - ciflow/inductor/*
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref_name }}-${{ github.ref_type == 'branch' && github.sha }}-${{ github.event_name == 'workflow_dispatch' }}
  cancel-in-progress: true

jobs:
  linux-bionic-cuda11_7-py3_10-gcc7-inductor-build:
    name: cuda11.7-py3.10-gcc7-sm86
    uses: ./.github/workflows/_linux-build.yml
    with:
      build-environment: linux-bionic-cuda11.7-py3.10-gcc7-sm86
      docker-image-name: pytorch-linux-bionic-cuda11.7-cudnn8-py3-gcc7
      cuda-arch-list: '8.6'
      test-matrix: |
        { include: [
          { config: "inductor", shard: 1, num_shards: 1, runner: "linux.g5.4xlarge.nvidia.gpu" },
          { config: "inductor_huggingface", shard: 1, num_shards: 1, runner: "linux.g5.4xlarge.nvidia.gpu" },
          { config: "inductor_timm", shard: 1, num_shards: 2, runner: "linux.g5.4xlarge.nvidia.gpu" },
          { config: "inductor_timm", shard: 2, num_shards: 2, runner: "linux.g5.4xlarge.nvidia.gpu" },
          { config: "inductor_torchbench", shard: 1, num_shards: 1, runner: "linux.g5.4xlarge.nvidia.gpu" },
          { config: "inductor_distributed", shard: 1, num_shards: 1, runner: "linux.g5.12xlarge.nvidia.gpu" },
        ]}

  linux-bionic-cuda11_7-py3_10-gcc7-inductor-test:
    name: cuda11.7-py3.10-gcc7-sm86
    uses: ./.github/workflows/_linux-test.yml
    needs: linux-bionic-cuda11_7-py3_10-gcc7-inductor-build
    with:
      build-environment: linux-bionic-cuda11.7-py3.10-gcc7-sm86
      docker-image: ${{ needs.linux-bionic-cuda11_7-py3_10-gcc7-inductor-build.outputs.docker-image }}
      test-matrix: ${{ needs.linux-bionic-cuda11_7-py3_10-gcc7-inductor-build.outputs.test-matrix }}

  linux-bionic-cuda11_7-py3_10-gcc7-inductor-build-gcp:
    name: cuda11.7-py3.10-gcc7-sm80
    uses: ./.github/workflows/_linux-build.yml
    with:
      build-environment: linux-bionic-cuda11.7-py3.10-gcc7-sm80
      docker-image-name: pytorch-linux-bionic-cuda11.7-cudnn8-py3-gcc7
      cuda-arch-list: '8.0'
      test-matrix: |
        { include: [
          { config: "inductor_torchbench_smoketest_perf", shard: 1, num_shards: 1, runner: "linux.gcp.a100" },
        ]}

  linux-bionic-cuda11_7-py3_10-gcc7-inductor-test-gcp:
    name: cuda11.7-py3.10-gcc7-sm80
    uses: ./.github/workflows/_linux-test.yml
    needs: linux-bionic-cuda11_7-py3_10-gcc7-inductor-build-gcp
    with:
      build-environment: linux-bionic-cuda11.7-py3.10-gcc7-sm80
      docker-image: ${{ needs.linux-bionic-cuda11_7-py3_10-gcc7-inductor-build-gcp.outputs.docker-image }}
      test-matrix: ${{ needs.linux-bionic-cuda11_7-py3_10-gcc7-inductor-build-gcp.outputs.test-matrix }}
      use-gha: anything-non-empty-to-use-gha
