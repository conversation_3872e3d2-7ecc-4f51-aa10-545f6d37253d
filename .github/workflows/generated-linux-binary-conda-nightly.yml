# @generated DO NOT EDIT MANUALLY

# Template is at:    .github/templates/linux_binary_build_workflow.yml.j2
# Generation script: .github/scripts/generate_ci_workflows.py
name: linux-binary-conda

on:
  push:
    # NOTE: Meta Employees can trigger new nightlies using: https://fburl.com/trigger_pytorch_nightly_build
    branches:
      - nightly
    tags:
      # NOTE: Binary build pipelines should only get triggered on release candidate builds
      # Release candidate tags look like: v1.11.0-rc1
      - v[0-9]+.[0-9]+.[0-9]+-rc[0-9]+
      - 'ciflow/binaries/*'
      - 'ciflow/binaries_conda/*'
  workflow_dispatch:

env:
  # Needed for conda builds
  ALPINE_IMAGE: "308535385114.dkr.ecr.us-east-1.amazonaws.com/tool/alpine"
  ANACONDA_USER: pytorch
  AWS_DEFAULT_REGION: us-east-1
  BINARY_ENV_FILE: /tmp/env
  BUILD_ENVIRONMENT: linux-binary-conda
  BUILDER_ROOT: /builder
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  PR_NUMBER: ${{ github.event.pull_request.number }}
  PYTORCH_FINAL_PACKAGE_DIR: /artifacts
  PYTORCH_ROOT: /pytorch
  SHA1: ${{ github.event.pull_request.head.sha || github.sha }}
  SKIP_ALL_TESTS: 1
concurrency:
  group: linux-binary-conda-${{ github.event.pull_request.number || github.ref_name }}-${{ github.ref_type == 'branch' && github.sha }}-${{ github.event_name == 'workflow_dispatch' }}
  cancel-in-progress: true

jobs:
  conda-py3_8-cpu-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/conda-builder:cpu
      DESIRED_PYTHON: "3.8"
      build_name: conda-py3_8-cpu
      build_environment: linux-binary-conda
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  conda-py3_8-cpu-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_8-cpu-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/conda-builder:cpu
      DESIRED_PYTHON: "3.8"
      build_name: conda-py3_8-cpu
      build_environment: linux-binary-conda
      runs_on: linux.4xlarge
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  conda-py3_8-cpu-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_8-cpu-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/conda-builder:cpu
      DESIRED_PYTHON: "3.8"
      build_name: conda-py3_8-cpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  conda-py3_8-cuda11_7-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.7
      DESIRED_PYTHON: "3.8"
      build_name: conda-py3_8-cuda11_7
      build_environment: linux-binary-conda
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  conda-py3_8-cuda11_7-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_8-cuda11_7-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.7
      DESIRED_PYTHON: "3.8"
      build_name: conda-py3_8-cuda11_7
      build_environment: linux-binary-conda
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  conda-py3_8-cuda11_7-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_8-cuda11_7-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.7
      DESIRED_PYTHON: "3.8"
      build_name: conda-py3_8-cuda11_7
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  conda-py3_8-cuda11_8-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.8
      DESIRED_PYTHON: "3.8"
      build_name: conda-py3_8-cuda11_8
      build_environment: linux-binary-conda
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  conda-py3_8-cuda11_8-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_8-cuda11_8-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.8
      DESIRED_PYTHON: "3.8"
      build_name: conda-py3_8-cuda11_8
      build_environment: linux-binary-conda
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  conda-py3_8-cuda11_8-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_8-cuda11_8-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.8
      DESIRED_PYTHON: "3.8"
      build_name: conda-py3_8-cuda11_8
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  conda-py3_9-cpu-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/conda-builder:cpu
      DESIRED_PYTHON: "3.9"
      build_name: conda-py3_9-cpu
      build_environment: linux-binary-conda
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  conda-py3_9-cpu-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_9-cpu-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/conda-builder:cpu
      DESIRED_PYTHON: "3.9"
      build_name: conda-py3_9-cpu
      build_environment: linux-binary-conda
      runs_on: linux.4xlarge
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  conda-py3_9-cpu-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_9-cpu-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/conda-builder:cpu
      DESIRED_PYTHON: "3.9"
      build_name: conda-py3_9-cpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  conda-py3_9-cuda11_7-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.7
      DESIRED_PYTHON: "3.9"
      build_name: conda-py3_9-cuda11_7
      build_environment: linux-binary-conda
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  conda-py3_9-cuda11_7-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_9-cuda11_7-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.7
      DESIRED_PYTHON: "3.9"
      build_name: conda-py3_9-cuda11_7
      build_environment: linux-binary-conda
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  conda-py3_9-cuda11_7-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_9-cuda11_7-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.7
      DESIRED_PYTHON: "3.9"
      build_name: conda-py3_9-cuda11_7
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  conda-py3_9-cuda11_8-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.8
      DESIRED_PYTHON: "3.9"
      build_name: conda-py3_9-cuda11_8
      build_environment: linux-binary-conda
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  conda-py3_9-cuda11_8-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_9-cuda11_8-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.8
      DESIRED_PYTHON: "3.9"
      build_name: conda-py3_9-cuda11_8
      build_environment: linux-binary-conda
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  conda-py3_9-cuda11_8-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_9-cuda11_8-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.8
      DESIRED_PYTHON: "3.9"
      build_name: conda-py3_9-cuda11_8
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  conda-py3_10-cpu-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/conda-builder:cpu
      DESIRED_PYTHON: "3.10"
      build_name: conda-py3_10-cpu
      build_environment: linux-binary-conda
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  conda-py3_10-cpu-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_10-cpu-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/conda-builder:cpu
      DESIRED_PYTHON: "3.10"
      build_name: conda-py3_10-cpu
      build_environment: linux-binary-conda
      runs_on: linux.4xlarge
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  conda-py3_10-cpu-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_10-cpu-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/conda-builder:cpu
      DESIRED_PYTHON: "3.10"
      build_name: conda-py3_10-cpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  conda-py3_10-cuda11_7-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.7
      DESIRED_PYTHON: "3.10"
      build_name: conda-py3_10-cuda11_7
      build_environment: linux-binary-conda
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  conda-py3_10-cuda11_7-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_10-cuda11_7-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.7
      DESIRED_PYTHON: "3.10"
      build_name: conda-py3_10-cuda11_7
      build_environment: linux-binary-conda
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  conda-py3_10-cuda11_7-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_10-cuda11_7-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.7
      DESIRED_PYTHON: "3.10"
      build_name: conda-py3_10-cuda11_7
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  conda-py3_10-cuda11_8-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.8
      DESIRED_PYTHON: "3.10"
      build_name: conda-py3_10-cuda11_8
      build_environment: linux-binary-conda
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  conda-py3_10-cuda11_8-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_10-cuda11_8-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.8
      DESIRED_PYTHON: "3.10"
      build_name: conda-py3_10-cuda11_8
      build_environment: linux-binary-conda
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  conda-py3_10-cuda11_8-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_10-cuda11_8-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.8
      DESIRED_PYTHON: "3.10"
      build_name: conda-py3_10-cuda11_8
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  conda-py3_11-cpu-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/conda-builder:cpu
      DESIRED_PYTHON: "3.11"
      build_name: conda-py3_11-cpu
      build_environment: linux-binary-conda
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  conda-py3_11-cpu-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_11-cpu-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/conda-builder:cpu
      DESIRED_PYTHON: "3.11"
      build_name: conda-py3_11-cpu
      build_environment: linux-binary-conda
      runs_on: linux.4xlarge
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  conda-py3_11-cpu-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_11-cpu-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/conda-builder:cpu
      DESIRED_PYTHON: "3.11"
      build_name: conda-py3_11-cpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  conda-py3_11-cuda11_7-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.7
      DESIRED_PYTHON: "3.11"
      build_name: conda-py3_11-cuda11_7
      build_environment: linux-binary-conda
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  conda-py3_11-cuda11_7-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_11-cuda11_7-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.7
      DESIRED_PYTHON: "3.11"
      build_name: conda-py3_11-cuda11_7
      build_environment: linux-binary-conda
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  conda-py3_11-cuda11_7-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_11-cuda11_7-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.7
      DESIRED_PYTHON: "3.11"
      build_name: conda-py3_11-cuda11_7
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  conda-py3_11-cuda11_8-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.8
      DESIRED_PYTHON: "3.11"
      build_name: conda-py3_11-cuda11_8
      build_environment: linux-binary-conda
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  conda-py3_11-cuda11_8-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_11-cuda11_8-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.8
      DESIRED_PYTHON: "3.11"
      build_name: conda-py3_11-cuda11_8
      build_environment: linux-binary-conda
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  conda-py3_11-cuda11_8-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: conda-py3_11-cuda11_8-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: conda
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/conda-builder:cuda11.8
      DESIRED_PYTHON: "3.11"
      build_name: conda-py3_11-cuda11_8
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
