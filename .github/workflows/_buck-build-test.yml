name: buck

on:
  workflow_call:

defaults:
  run:
    shell: bash -e -l {0}

jobs:
  buck-build-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout PyTorch
        uses: pytorch/pytorch/.github/actions/checkout-pytorch@master

      - name: Set up JDK 8
        uses: actions/setup-java@v3
        with:
          java-version: '8'
          distribution: 'temurin'

      - name: Setup miniconda
        uses: pytorch/test-infra/.github/actions/setup-miniconda@main
        with:
          python-version: 3.8
          environment-file: .github/requirements/conda-env-${{ runner.os }}-${{ runner.arch }}

      - name: Install Buck
        uses: nick-fields/retry@3e91a01664abd3c5cd539100d10d33b9c5b68482
        with:
          timeout_minutes: 10
          max_attempts: 5
          command: |
            sudo apt update -q
            wget -q https://github.com/facebook/buck/releases/download/v2021.01.12.01/buck.2021.01.12.01_all.deb
            sudo apt install ./buck.2021.01.12.01_all.deb

      - name: Download third party libraries and generate wrappers
        uses: nick-fields/retry@3e91a01664abd3c5cd539100d10d33b9c5b68482
        with:
          timeout_minutes: 10
          max_attempts: 5
          command: |
            sh scripts/buck_setup.sh

      - name: Build tools
        run: |
          buck build tools: --keep-going

      - name: Run tools tests
        run: |
          buck test tools:selective_build_test tools:gen_oplist_test tools:gen_operators_yaml_test

      - name: Build c10
        run: |
          buck build c10:c10

      - name: Build XNNPACK
        run: |
          buck build third_party:XNNPACK

      - name: Build QNNPACK
        run: |
          buck build aten/src/ATen/native/quantized/cpu/qnnpack:pytorch_qnnpack

      - name: Test QNNPACK
        run: |
          buck test aten/src/ATen/native/quantized/cpu/qnnpack:pytorch_qnnpack_test

      - name: Build aten_cpu
        run: |
          buck build :aten_cpu

      - name: Build torch_mobile_core
        run: |
          buck build :torch_mobile_core

      - name: Build pt_ops_full
        run: |
          buck build :pt_ops_full

      - name: Build mobile benchmark
        run: |
          buck build :ptmobile_benchmark

      - name: Run lite interpreter model
        run: |
          buck run :ptmobile_benchmark -- --model=ios/TestApp/models/mobilenet_v2.ptl --input_dims=1,3,224,224 --input_type=float

      - name: Build everything
        run: |
          buck build //... --keep-going
