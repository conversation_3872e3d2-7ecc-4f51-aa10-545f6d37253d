name: Auto Request Review

on:
  pull_request:
    types: [opened, ready_for_review, reopened]

jobs:
  auto-request-review:
    # Don't run on forked repos
    if: ${{ !github.event.pull_request.head.repo.fork }}
    name: Auto Request Review
    runs-on: ubuntu-latest
    steps:
      - name: Request review based on files changes and/or groups the author belongs to
        # v0.7.0
        uses: necojackarc/auto-request-review@e08cdffa277d50854744de3f76230260e61c67f4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.sha }}-${{ github.event_name == 'workflow_dispatch' }}
  cancel-in-progress: true
