name: docker-builds

on:
  workflow_dispatch:
  pull_request:
    paths:
      - .ci/docker/**
      - .github/workflows/docker-builds.yml
  push:
    branches:
      - master
      - main
      - release/*
      - landchecks/*
    paths:
      - .ci/docker/**
      - .github/workflows/docker-builds.yml
  schedule:
    - cron: 1 3 * * 3

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.sha }}-${{ github.event_name == 'workflow_dispatch' }}
  cancel-in-progress: true

env:
  ALPINE_IMAGE: 308535385114.dkr.ecr.us-east-1.amazonaws.com/tool/alpine
  AWS_DEFAULT_REGION: us-east-1

jobs:
  docker-build:
    runs-on: [self-hosted, linux.2xlarge]
    timeout-minutes: 240
    strategy:
      fail-fast: false
      matrix:
        include:
          - docker-image-name: pytorch-linux-bionic-cuda11.6-cudnn8-py3-gcc7
          - docker-image-name: pytorch-linux-bionic-cuda11.7-cudnn8-py3-gcc7
          - docker-image-name: pytorch-linux-bionic-cuda11.8-cudnn8-py3-gcc7
          - docker-image-name: pytorch-linux-bionic-py3.8-clang9
          - docker-image-name: pytorch-linux-bionic-py3.11-clang9
          - docker-image-name: pytorch-linux-focal-rocm-n-1-py3
          - docker-image-name: pytorch-linux-focal-rocm-n-py3
          - docker-image-name: pytorch-linux-jammy-cuda11.6-cudnn8-py3.8-clang12
          - docker-image-name: pytorch-linux-jammy-cuda11.7-cudnn8-py3.8-clang12
          - docker-image-name: pytorch-linux-jammy-cuda11.8-cudnn8-py3.8-clang12
          - docker-image-name: pytorch-linux-focal-py3-clang7-android-ndk-r19c
          - docker-image-name: pytorch-linux-focal-py3.8-gcc7
          - docker-image-name: pytorch-linux-focal-py3-clang7-asan
          - docker-image-name: pytorch-linux-focal-py3-clang10-onnx
          - docker-image-name: pytorch-linux-focal-linter
    env:
      DOCKER_IMAGE_BASE: 308535385114.dkr.ecr.us-east-1.amazonaws.com/pytorch/${{ matrix.docker-image-name }}
    steps:
      - name: Clean workspace
        shell: bash
        run: |
          echo "${GITHUB_WORKSPACE}"
          sudo rm -rf "${GITHUB_WORKSPACE}"
          mkdir "${GITHUB_WORKSPACE}"

      # [see note: pytorch repo ref]
      # deep clone (fetch-depth 0) required for git merge-base
      - name: Checkout PyTorch
        uses: pytorch/pytorch/.github/actions/checkout-pytorch@master

      - name: Setup Linux
        uses: ./.github/actions/setup-linux

      - name: Build docker image
        id: build-docker-image
        uses: ./.github/actions/calculate-docker-image
        with:
          docker-image-name: ${{ matrix.docker-image-name }}
          always-rebuild: true
          skip_push: false
          force_push: true

      - name: Pull docker image
        uses: pytorch/test-infra/.github/actions/pull-docker-image@main
        with:
          docker-image: ${{ steps.build-docker-image.outputs.docker-image }}

      - uses: nick-fields/retry@3e91a01664abd3c5cd539100d10d33b9c5b68482
        name: Push to https://https://ghcr.io/
        id: push-to-ghcr-io
        if: ${{ github.event_name == 'push' }}
        env:
          ECR_DOCKER_IMAGE: ${{ steps.build-docker-image.outputs.docker-image }}
          GHCR_PAT: ${{ secrets.GHCR_PAT }}
          IMAGE_NAME: ${{ matrix.docker-image-name }}
        with:
          shell: bash
          timeout_minutes: 15
          max_attempts: 5
          retry_wait_seconds: 90
          command: |
            ghcr_image="ghcr.io/pytorch/ci-image"
            tag=${ECR_DOCKER_IMAGE##*:}
            # Push docker image to the ghcr.io
            echo $GHCR_PAT | docker login ghcr.io -u pytorch --password-stdin
            docker tag "${ECR_DOCKER_IMAGE}" "${ghcr_image}:${IMAGE_NAME}-${tag}"
            docker push "${ghcr_image}:${IMAGE_NAME}-${tag}"

      - name: Chown workspace
        uses: ./.github/actions/chown-workspace
        if: always()

      - name: Teardown Linux
        uses: pytorch/test-infra/.github/actions/teardown-linux@main

        if: always()
