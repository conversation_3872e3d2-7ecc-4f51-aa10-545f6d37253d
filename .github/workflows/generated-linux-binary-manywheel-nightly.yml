# @generated DO NOT EDIT MANUALLY

# Template is at:    .github/templates/linux_binary_build_workflow.yml.j2
# Generation script: .github/scripts/generate_ci_workflows.py
name: linux-binary-manywheel

on:
  push:
    # NOTE: Meta Employees can trigger new nightlies using: https://fburl.com/trigger_pytorch_nightly_build
    branches:
      - nightly
    tags:
      # NOTE: Binary build pipelines should only get triggered on release candidate builds
      # Release candidate tags look like: v1.11.0-rc1
      - v[0-9]+.[0-9]+.[0-9]+-rc[0-9]+
      - 'ciflow/binaries/*'
      - 'ciflow/binaries_wheel/*'
  workflow_dispatch:

env:
  # Needed for conda builds
  ALPINE_IMAGE: "308535385114.dkr.ecr.us-east-1.amazonaws.com/tool/alpine"
  ANACONDA_USER: pytorch
  AWS_DEFAULT_REGION: us-east-1
  BINARY_ENV_FILE: /tmp/env
  BUILD_ENVIRONMENT: linux-binary-manywheel
  BUILDER_ROOT: /builder
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  PR_NUMBER: ${{ github.event.pull_request.number }}
  PYTORCH_FINAL_PACKAGE_DIR: /artifacts
  PYTORCH_ROOT: /pytorch
  SHA1: ${{ github.event.pull_request.head.sha || github.sha }}
  SKIP_ALL_TESTS: 1
concurrency:
  group: linux-binary-manywheel-${{ github.event.pull_request.number || github.ref_name }}-${{ github.ref_type == 'branch' && github.sha }}-${{ github.event_name == 'workflow_dispatch' }}
  cancel-in-progress: true

jobs:
  manywheel-py3_8-cpu-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/manylinux-builder:cpu
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-cpu
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_8-cpu-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_8-cpu-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/manylinux-builder:cpu
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-cpu
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_8-cpu-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_8-cpu-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/manylinux-builder:cpu
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-cpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_8-cpu-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu-cxx11-abi
      GPU_ARCH_TYPE: cpu-cxx11-abi
      DOCKER_IMAGE: pytorch/manylinuxcxx11-abi-builder:cpu-cxx11-abi
      DESIRED_DEVTOOLSET: cxx11-abi
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-cpu-cxx11-abi
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_8-cpu-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_8-cpu-cxx11-abi-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu-cxx11-abi
      GPU_ARCH_TYPE: cpu-cxx11-abi
      DOCKER_IMAGE: pytorch/manylinuxcxx11-abi-builder:cpu-cxx11-abi
      DESIRED_DEVTOOLSET: cxx11-abi
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-cpu-cxx11-abi
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_8-cpu-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_8-cpu-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu-cxx11-abi
      GPU_ARCH_TYPE: cpu-cxx11-abi
      DOCKER_IMAGE: pytorch/manylinuxcxx11-abi-builder:cpu-cxx11-abi
      DESIRED_DEVTOOLSET: cxx11-abi
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-cpu-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_8-cuda11_7-with-pypi-cudnn-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-cuda11_7-with-pypi-cudnn
      build_environment: linux-binary-manywheel
      PYTORCH_EXTRA_INSTALL_REQUIREMENTS: nvidia-cuda-nvrtc-cu11==11.7.99; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cuda-runtime-cu11==11.7.99; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cuda-cupti-cu11==11.7.101; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cudnn-cu11==8.5.0.96; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cublas-cu11==11.10.3.66; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cufft-cu11==10.9.0.58; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-curand-cu11==10.2.10.91; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cusolver-cu11==11.4.0.1; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cusparse-cu11==11.7.4.91; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-nccl-cu11==2.14.3; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-nvtx-cu11==11.7.91; platform_system == 'Linux' and platform_machine == 'x86_64' | triton==2.0.0; platform_system == 'Linux' and platform_machine == 'x86_64'
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_8-cuda11_7-with-pypi-cudnn-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_8-cuda11_7-with-pypi-cudnn-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-cuda11_7-with-pypi-cudnn
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_8-cuda11_7-with-pypi-cudnn-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_8-cuda11_7-with-pypi-cudnn-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-cuda11_7-with-pypi-cudnn
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_8-cuda11_7-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-cuda11_7
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_8-cuda11_7-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_8-cuda11_7-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-cuda11_7
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_8-cuda11_7-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_8-cuda11_7-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-cuda11_7
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_8-cuda11_8-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.8
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-cuda11_8
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_8-cuda11_8-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_8-cuda11_8-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.8
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-cuda11_8
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_8-cuda11_8-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_8-cuda11_8-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.8
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-cuda11_8
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_8-rocm5_3-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.3
      GPU_ARCH_VERSION: 5.3
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.3
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-rocm5_3
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_8-rocm5_3-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_8-rocm5_3-build
    runs-on: linux.rocm.gpu
    timeout-minutes: 240
    env:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.3
      GPU_ARCH_VERSION: 5.3
      GPU_ARCH_TYPE: rocm
      SKIP_ALL_TESTS: 1
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.3
      DESIRED_PYTHON: "3.8"
    steps:
      - name: Setup ROCm
        uses: ./.github/actions/setup-rocm
      - uses: actions/download-artifact@v3
        name: Download Build Artifacts
        with:
          name: manywheel-py3_8-rocm5_3
          path: "${{ runner.temp }}/artifacts/"
      - name: Checkout PyTorch
        uses: malfet/checkout@silent-checkout
        with:
          submodules: recursive
          path: pytorch
          quiet-checkout: true
      - name: Clean PyTorch checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: pytorch
      - name: Checkout pytorch/builder
        uses: malfet/checkout@silent-checkout
        with:
          ref: release/2.0
          submodules: recursive
          repository: pytorch/builder
          path: builder
          quiet-checkout: true
      - name: Clean pytorch/builder checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: builder
      - name: ROCm set GPU_FLAG
        run: |
          echo "GPU_FLAG=--device=/dev/mem --device=/dev/kfd --device=/dev/dri --group-add video --group-add daemon" >> "${GITHUB_ENV}"
      - name: Pull Docker image
        uses: pytorch/test-infra/.github/actions/pull-docker-image@main
        with:
          docker-image: pytorch/manylinux-builder:rocm5.3
      - name: Test Pytorch binary
        uses: ./pytorch/.github/actions/test-pytorch-binary
      - name: Teardown ROCm
        uses: ./.github/actions/teardown-rocm
  manywheel-py3_8-rocm5_3-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_8-rocm5_3-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.3
      GPU_ARCH_VERSION: 5.3
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.3
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-rocm5_3
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_8-rocm5_4_2-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.4.2
      GPU_ARCH_VERSION: 5.4.2
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.4.2
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-rocm5_4_2
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_8-rocm5_4_2-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_8-rocm5_4_2-build
    runs-on: linux.rocm.gpu
    timeout-minutes: 240
    env:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.4.2
      GPU_ARCH_VERSION: 5.4.2
      GPU_ARCH_TYPE: rocm
      SKIP_ALL_TESTS: 1
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.4.2
      DESIRED_PYTHON: "3.8"
    steps:
      - name: Setup ROCm
        uses: ./.github/actions/setup-rocm
      - uses: actions/download-artifact@v3
        name: Download Build Artifacts
        with:
          name: manywheel-py3_8-rocm5_4_2
          path: "${{ runner.temp }}/artifacts/"
      - name: Checkout PyTorch
        uses: malfet/checkout@silent-checkout
        with:
          submodules: recursive
          path: pytorch
          quiet-checkout: true
      - name: Clean PyTorch checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: pytorch
      - name: Checkout pytorch/builder
        uses: malfet/checkout@silent-checkout
        with:
          ref: release/2.0
          submodules: recursive
          repository: pytorch/builder
          path: builder
          quiet-checkout: true
      - name: Clean pytorch/builder checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: builder
      - name: ROCm set GPU_FLAG
        run: |
          echo "GPU_FLAG=--device=/dev/mem --device=/dev/kfd --device=/dev/dri --group-add video --group-add daemon" >> "${GITHUB_ENV}"
      - name: Pull Docker image
        uses: pytorch/test-infra/.github/actions/pull-docker-image@main
        with:
          docker-image: pytorch/manylinux-builder:rocm5.4.2
      - name: Test Pytorch binary
        uses: ./pytorch/.github/actions/test-pytorch-binary
      - name: Teardown ROCm
        uses: ./.github/actions/teardown-rocm
  manywheel-py3_8-rocm5_4_2-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_8-rocm5_4_2-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.4.2
      GPU_ARCH_VERSION: 5.4.2
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.4.2
      DESIRED_PYTHON: "3.8"
      build_name: manywheel-py3_8-rocm5_4_2
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_9-cpu-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/manylinux-builder:cpu
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-cpu
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_9-cpu-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_9-cpu-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/manylinux-builder:cpu
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-cpu
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_9-cpu-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_9-cpu-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/manylinux-builder:cpu
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-cpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_9-cpu-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu-cxx11-abi
      GPU_ARCH_TYPE: cpu-cxx11-abi
      DOCKER_IMAGE: pytorch/manylinuxcxx11-abi-builder:cpu-cxx11-abi
      DESIRED_DEVTOOLSET: cxx11-abi
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-cpu-cxx11-abi
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_9-cpu-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_9-cpu-cxx11-abi-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu-cxx11-abi
      GPU_ARCH_TYPE: cpu-cxx11-abi
      DOCKER_IMAGE: pytorch/manylinuxcxx11-abi-builder:cpu-cxx11-abi
      DESIRED_DEVTOOLSET: cxx11-abi
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-cpu-cxx11-abi
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_9-cpu-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_9-cpu-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu-cxx11-abi
      GPU_ARCH_TYPE: cpu-cxx11-abi
      DOCKER_IMAGE: pytorch/manylinuxcxx11-abi-builder:cpu-cxx11-abi
      DESIRED_DEVTOOLSET: cxx11-abi
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-cpu-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_9-cuda11_7-with-pypi-cudnn-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-cuda11_7-with-pypi-cudnn
      build_environment: linux-binary-manywheel
      PYTORCH_EXTRA_INSTALL_REQUIREMENTS: nvidia-cuda-nvrtc-cu11==11.7.99; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cuda-runtime-cu11==11.7.99; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cuda-cupti-cu11==11.7.101; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cudnn-cu11==8.5.0.96; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cublas-cu11==11.10.3.66; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cufft-cu11==10.9.0.58; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-curand-cu11==10.2.10.91; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cusolver-cu11==11.4.0.1; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cusparse-cu11==11.7.4.91; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-nccl-cu11==2.14.3; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-nvtx-cu11==11.7.91; platform_system == 'Linux' and platform_machine == 'x86_64' | triton==2.0.0; platform_system == 'Linux' and platform_machine == 'x86_64'
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_9-cuda11_7-with-pypi-cudnn-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_9-cuda11_7-with-pypi-cudnn-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-cuda11_7-with-pypi-cudnn
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_9-cuda11_7-with-pypi-cudnn-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_9-cuda11_7-with-pypi-cudnn-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-cuda11_7-with-pypi-cudnn
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_9-cuda11_7-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-cuda11_7
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_9-cuda11_7-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_9-cuda11_7-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-cuda11_7
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_9-cuda11_7-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_9-cuda11_7-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-cuda11_7
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_9-cuda11_8-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.8
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-cuda11_8
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_9-cuda11_8-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_9-cuda11_8-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.8
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-cuda11_8
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_9-cuda11_8-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_9-cuda11_8-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.8
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-cuda11_8
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_9-rocm5_3-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.3
      GPU_ARCH_VERSION: 5.3
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.3
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-rocm5_3
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_9-rocm5_3-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_9-rocm5_3-build
    runs-on: linux.rocm.gpu
    timeout-minutes: 240
    env:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.3
      GPU_ARCH_VERSION: 5.3
      GPU_ARCH_TYPE: rocm
      SKIP_ALL_TESTS: 1
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.3
      DESIRED_PYTHON: "3.9"
    steps:
      - name: Setup ROCm
        uses: ./.github/actions/setup-rocm
      - uses: actions/download-artifact@v3
        name: Download Build Artifacts
        with:
          name: manywheel-py3_9-rocm5_3
          path: "${{ runner.temp }}/artifacts/"
      - name: Checkout PyTorch
        uses: malfet/checkout@silent-checkout
        with:
          submodules: recursive
          path: pytorch
          quiet-checkout: true
      - name: Clean PyTorch checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: pytorch
      - name: Checkout pytorch/builder
        uses: malfet/checkout@silent-checkout
        with:
          ref: release/2.0
          submodules: recursive
          repository: pytorch/builder
          path: builder
          quiet-checkout: true
      - name: Clean pytorch/builder checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: builder
      - name: ROCm set GPU_FLAG
        run: |
          echo "GPU_FLAG=--device=/dev/mem --device=/dev/kfd --device=/dev/dri --group-add video --group-add daemon" >> "${GITHUB_ENV}"
      - name: Pull Docker image
        uses: pytorch/test-infra/.github/actions/pull-docker-image@main
        with:
          docker-image: pytorch/manylinux-builder:rocm5.3
      - name: Test Pytorch binary
        uses: ./pytorch/.github/actions/test-pytorch-binary
      - name: Teardown ROCm
        uses: ./.github/actions/teardown-rocm
  manywheel-py3_9-rocm5_3-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_9-rocm5_3-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.3
      GPU_ARCH_VERSION: 5.3
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.3
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-rocm5_3
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_9-rocm5_4_2-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.4.2
      GPU_ARCH_VERSION: 5.4.2
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.4.2
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-rocm5_4_2
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_9-rocm5_4_2-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_9-rocm5_4_2-build
    runs-on: linux.rocm.gpu
    timeout-minutes: 240
    env:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.4.2
      GPU_ARCH_VERSION: 5.4.2
      GPU_ARCH_TYPE: rocm
      SKIP_ALL_TESTS: 1
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.4.2
      DESIRED_PYTHON: "3.9"
    steps:
      - name: Setup ROCm
        uses: ./.github/actions/setup-rocm
      - uses: actions/download-artifact@v3
        name: Download Build Artifacts
        with:
          name: manywheel-py3_9-rocm5_4_2
          path: "${{ runner.temp }}/artifacts/"
      - name: Checkout PyTorch
        uses: malfet/checkout@silent-checkout
        with:
          submodules: recursive
          path: pytorch
          quiet-checkout: true
      - name: Clean PyTorch checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: pytorch
      - name: Checkout pytorch/builder
        uses: malfet/checkout@silent-checkout
        with:
          ref: release/2.0
          submodules: recursive
          repository: pytorch/builder
          path: builder
          quiet-checkout: true
      - name: Clean pytorch/builder checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: builder
      - name: ROCm set GPU_FLAG
        run: |
          echo "GPU_FLAG=--device=/dev/mem --device=/dev/kfd --device=/dev/dri --group-add video --group-add daemon" >> "${GITHUB_ENV}"
      - name: Pull Docker image
        uses: pytorch/test-infra/.github/actions/pull-docker-image@main
        with:
          docker-image: pytorch/manylinux-builder:rocm5.4.2
      - name: Test Pytorch binary
        uses: ./pytorch/.github/actions/test-pytorch-binary
      - name: Teardown ROCm
        uses: ./.github/actions/teardown-rocm
  manywheel-py3_9-rocm5_4_2-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_9-rocm5_4_2-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.4.2
      GPU_ARCH_VERSION: 5.4.2
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.4.2
      DESIRED_PYTHON: "3.9"
      build_name: manywheel-py3_9-rocm5_4_2
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_10-cpu-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/manylinux-builder:cpu
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-cpu
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_10-cpu-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_10-cpu-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/manylinux-builder:cpu
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-cpu
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_10-cpu-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_10-cpu-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/manylinux-builder:cpu
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-cpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_10-cpu-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu-cxx11-abi
      GPU_ARCH_TYPE: cpu-cxx11-abi
      DOCKER_IMAGE: pytorch/manylinuxcxx11-abi-builder:cpu-cxx11-abi
      DESIRED_DEVTOOLSET: cxx11-abi
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-cpu-cxx11-abi
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_10-cpu-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_10-cpu-cxx11-abi-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu-cxx11-abi
      GPU_ARCH_TYPE: cpu-cxx11-abi
      DOCKER_IMAGE: pytorch/manylinuxcxx11-abi-builder:cpu-cxx11-abi
      DESIRED_DEVTOOLSET: cxx11-abi
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-cpu-cxx11-abi
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_10-cpu-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_10-cpu-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu-cxx11-abi
      GPU_ARCH_TYPE: cpu-cxx11-abi
      DOCKER_IMAGE: pytorch/manylinuxcxx11-abi-builder:cpu-cxx11-abi
      DESIRED_DEVTOOLSET: cxx11-abi
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-cpu-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_10-cuda11_7-with-pypi-cudnn-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-cuda11_7-with-pypi-cudnn
      build_environment: linux-binary-manywheel
      PYTORCH_EXTRA_INSTALL_REQUIREMENTS: nvidia-cuda-nvrtc-cu11==11.7.99; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cuda-runtime-cu11==11.7.99; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cuda-cupti-cu11==11.7.101; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cudnn-cu11==8.5.0.96; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cublas-cu11==11.10.3.66; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cufft-cu11==10.9.0.58; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-curand-cu11==10.2.10.91; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cusolver-cu11==11.4.0.1; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cusparse-cu11==11.7.4.91; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-nccl-cu11==2.14.3; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-nvtx-cu11==11.7.91; platform_system == 'Linux' and platform_machine == 'x86_64' | triton==2.0.0; platform_system == 'Linux' and platform_machine == 'x86_64'
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_10-cuda11_7-with-pypi-cudnn-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_10-cuda11_7-with-pypi-cudnn-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-cuda11_7-with-pypi-cudnn
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_10-cuda11_7-with-pypi-cudnn-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_10-cuda11_7-with-pypi-cudnn-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-cuda11_7-with-pypi-cudnn
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_10-cuda11_7-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-cuda11_7
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_10-cuda11_7-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_10-cuda11_7-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-cuda11_7
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_10-cuda11_7-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_10-cuda11_7-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-cuda11_7
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_10-cuda11_8-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.8
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-cuda11_8
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_10-cuda11_8-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_10-cuda11_8-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.8
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-cuda11_8
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_10-cuda11_8-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_10-cuda11_8-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.8
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-cuda11_8
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_10-rocm5_3-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.3
      GPU_ARCH_VERSION: 5.3
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.3
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-rocm5_3
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_10-rocm5_3-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_10-rocm5_3-build
    runs-on: linux.rocm.gpu
    timeout-minutes: 240
    env:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.3
      GPU_ARCH_VERSION: 5.3
      GPU_ARCH_TYPE: rocm
      SKIP_ALL_TESTS: 1
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.3
      DESIRED_PYTHON: "3.10"
    steps:
      - name: Setup ROCm
        uses: ./.github/actions/setup-rocm
      - uses: actions/download-artifact@v3
        name: Download Build Artifacts
        with:
          name: manywheel-py3_10-rocm5_3
          path: "${{ runner.temp }}/artifacts/"
      - name: Checkout PyTorch
        uses: malfet/checkout@silent-checkout
        with:
          submodules: recursive
          path: pytorch
          quiet-checkout: true
      - name: Clean PyTorch checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: pytorch
      - name: Checkout pytorch/builder
        uses: malfet/checkout@silent-checkout
        with:
          ref: release/2.0
          submodules: recursive
          repository: pytorch/builder
          path: builder
          quiet-checkout: true
      - name: Clean pytorch/builder checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: builder
      - name: ROCm set GPU_FLAG
        run: |
          echo "GPU_FLAG=--device=/dev/mem --device=/dev/kfd --device=/dev/dri --group-add video --group-add daemon" >> "${GITHUB_ENV}"
      - name: Pull Docker image
        uses: pytorch/test-infra/.github/actions/pull-docker-image@main
        with:
          docker-image: pytorch/manylinux-builder:rocm5.3
      - name: Test Pytorch binary
        uses: ./pytorch/.github/actions/test-pytorch-binary
      - name: Teardown ROCm
        uses: ./.github/actions/teardown-rocm
  manywheel-py3_10-rocm5_3-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_10-rocm5_3-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.3
      GPU_ARCH_VERSION: 5.3
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.3
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-rocm5_3
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_10-rocm5_4_2-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.4.2
      GPU_ARCH_VERSION: 5.4.2
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.4.2
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-rocm5_4_2
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_10-rocm5_4_2-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_10-rocm5_4_2-build
    runs-on: linux.rocm.gpu
    timeout-minutes: 240
    env:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.4.2
      GPU_ARCH_VERSION: 5.4.2
      GPU_ARCH_TYPE: rocm
      SKIP_ALL_TESTS: 1
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.4.2
      DESIRED_PYTHON: "3.10"
    steps:
      - name: Setup ROCm
        uses: ./.github/actions/setup-rocm
      - uses: actions/download-artifact@v3
        name: Download Build Artifacts
        with:
          name: manywheel-py3_10-rocm5_4_2
          path: "${{ runner.temp }}/artifacts/"
      - name: Checkout PyTorch
        uses: malfet/checkout@silent-checkout
        with:
          submodules: recursive
          path: pytorch
          quiet-checkout: true
      - name: Clean PyTorch checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: pytorch
      - name: Checkout pytorch/builder
        uses: malfet/checkout@silent-checkout
        with:
          ref: release/2.0
          submodules: recursive
          repository: pytorch/builder
          path: builder
          quiet-checkout: true
      - name: Clean pytorch/builder checkout
        run: |
          # Remove any artifacts from the previous checkouts
          git clean -fxd
        working-directory: builder
      - name: ROCm set GPU_FLAG
        run: |
          echo "GPU_FLAG=--device=/dev/mem --device=/dev/kfd --device=/dev/dri --group-add video --group-add daemon" >> "${GITHUB_ENV}"
      - name: Pull Docker image
        uses: pytorch/test-infra/.github/actions/pull-docker-image@main
        with:
          docker-image: pytorch/manylinux-builder:rocm5.4.2
      - name: Test Pytorch binary
        uses: ./pytorch/.github/actions/test-pytorch-binary
      - name: Teardown ROCm
        uses: ./.github/actions/teardown-rocm
  manywheel-py3_10-rocm5_4_2-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_10-rocm5_4_2-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: rocm5.4.2
      GPU_ARCH_VERSION: 5.4.2
      GPU_ARCH_TYPE: rocm
      DOCKER_IMAGE: pytorch/manylinux-builder:rocm5.4.2
      DESIRED_PYTHON: "3.10"
      build_name: manywheel-py3_10-rocm5_4_2
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_11-cpu-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/manylinux-builder:cpu
      DESIRED_PYTHON: "3.11"
      build_name: manywheel-py3_11-cpu
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_11-cpu-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_11-cpu-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/manylinux-builder:cpu
      DESIRED_PYTHON: "3.11"
      build_name: manywheel-py3_11-cpu
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_11-cpu-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_11-cpu-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu
      GPU_ARCH_TYPE: cpu
      DOCKER_IMAGE: pytorch/manylinux-builder:cpu
      DESIRED_PYTHON: "3.11"
      build_name: manywheel-py3_11-cpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_11-cpu-cxx11-abi-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu-cxx11-abi
      GPU_ARCH_TYPE: cpu-cxx11-abi
      DOCKER_IMAGE: pytorch/manylinuxcxx11-abi-builder:cpu-cxx11-abi
      DESIRED_DEVTOOLSET: cxx11-abi
      DESIRED_PYTHON: "3.11"
      build_name: manywheel-py3_11-cpu-cxx11-abi
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_11-cpu-cxx11-abi-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_11-cpu-cxx11-abi-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu-cxx11-abi
      GPU_ARCH_TYPE: cpu-cxx11-abi
      DOCKER_IMAGE: pytorch/manylinuxcxx11-abi-builder:cpu-cxx11-abi
      DESIRED_DEVTOOLSET: cxx11-abi
      DESIRED_PYTHON: "3.11"
      build_name: manywheel-py3_11-cpu-cxx11-abi
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_11-cpu-cxx11-abi-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_11-cpu-cxx11-abi-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cpu-cxx11-abi
      GPU_ARCH_TYPE: cpu-cxx11-abi
      DOCKER_IMAGE: pytorch/manylinuxcxx11-abi-builder:cpu-cxx11-abi
      DESIRED_DEVTOOLSET: cxx11-abi
      DESIRED_PYTHON: "3.11"
      build_name: manywheel-py3_11-cpu-cxx11-abi
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_11-cuda11_7-with-pypi-cudnn-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.11"
      build_name: manywheel-py3_11-cuda11_7-with-pypi-cudnn
      build_environment: linux-binary-manywheel
      PYTORCH_EXTRA_INSTALL_REQUIREMENTS: nvidia-cuda-nvrtc-cu11==11.7.99; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cuda-runtime-cu11==11.7.99; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cuda-cupti-cu11==11.7.101; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cudnn-cu11==8.5.0.96; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cublas-cu11==11.10.3.66; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cufft-cu11==10.9.0.58; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-curand-cu11==10.2.10.91; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cusolver-cu11==11.4.0.1; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-cusparse-cu11==11.7.4.91; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-nccl-cu11==2.14.3; platform_system == 'Linux' and platform_machine == 'x86_64' | nvidia-nvtx-cu11==11.7.91; platform_system == 'Linux' and platform_machine == 'x86_64' | triton==2.0.0; platform_system == 'Linux' and platform_machine == 'x86_64'
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_11-cuda11_7-with-pypi-cudnn-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_11-cuda11_7-with-pypi-cudnn-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.11"
      build_name: manywheel-py3_11-cuda11_7-with-pypi-cudnn
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_11-cuda11_7-with-pypi-cudnn-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_11-cuda11_7-with-pypi-cudnn-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.11"
      build_name: manywheel-py3_11-cuda11_7-with-pypi-cudnn
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_11-cuda11_7-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.11"
      build_name: manywheel-py3_11-cuda11_7
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_11-cuda11_7-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_11-cuda11_7-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.11"
      build_name: manywheel-py3_11-cuda11_7
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_11-cuda11_7-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_11-cuda11_7-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu117
      GPU_ARCH_VERSION: 11.7
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.7
      DESIRED_PYTHON: "3.11"
      build_name: manywheel-py3_11-cuda11_7
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
  manywheel-py3_11-cuda11_8-build:
    if: ${{ github.repository_owner == 'pytorch' }}
    uses: ./.github/workflows/_binary-build-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.8
      DESIRED_PYTHON: "3.11"
      build_name: manywheel-py3_11-cuda11_8
      build_environment: linux-binary-manywheel
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  manywheel-py3_11-cuda11_8-test:  # Testing
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_11-cuda11_8-build
    uses: ./.github/workflows/_binary-test-linux.yml
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.8
      DESIRED_PYTHON: "3.11"
      build_name: manywheel-py3_11-cuda11_8
      build_environment: linux-binary-manywheel
      runs_on: linux.4xlarge.nvidia.gpu
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
  manywheel-py3_11-cuda11_8-upload:  # Uploading
    if: ${{ github.repository_owner == 'pytorch' }}
    needs: manywheel-py3_11-cuda11_8-test
    with:
      PYTORCH_ROOT: /pytorch
      BUILDER_ROOT: /builder
      PACKAGE_TYPE: manywheel
      # TODO: This is a legacy variable that we eventually want to get rid of in
      #       favor of GPU_ARCH_VERSION
      DESIRED_CUDA: cu118
      GPU_ARCH_VERSION: 11.8
      GPU_ARCH_TYPE: cuda
      DOCKER_IMAGE: pytorch/manylinux-builder:cuda11.8
      DESIRED_PYTHON: "3.11"
      build_name: manywheel-py3_11-cuda11_8
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_PYTORCH_UPLOADER_ACCESS_KEY_ID }}
      aws-pytorch-uploader-secret-access-key: ${{ secrets.AWS_PYTORCH_UPLOADER_SECRET_ACCESS_KEY }}
      conda-pytorchbot-token: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    uses: ./.github/workflows/_binary-upload.yml
