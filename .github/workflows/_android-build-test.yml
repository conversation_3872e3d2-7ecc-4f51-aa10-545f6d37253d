name: android-build-test

on:
  workflow_call:
    inputs:
      build-environment:
        required: true
        type: string
        description: Top-level label for what's being built/tested.
      docker-image-name:
        required: true
        type: string
        description: Name of the base docker image to build with.
      sync-tag:
        required: false
        type: string
        default: ""
        description: |
          If this is set, our linter will use this to make sure that every other
          job with the same `sync-tag` is identical.

env:
  GIT_DEFAULT_BRANCH: ${{ github.event.repository.default_branch }}

jobs:
  build-and-test:
    # Don't run on forked repos.
    if: github.repository_owner == 'pytorch'
    runs-on: [self-hosted, linux.2xlarge]
    steps:
      - name: Setup SSH (Click me for login details)
        uses: pytorch/test-infra/.github/actions/setup-ssh@main
        with:
          github-secret: ${{ secrets.GITHUB_TOKEN }}

      # [see note: pytorch repo ref]
      - name: Checkout PyTorch
        uses: pytorch/pytorch/.github/actions/checkout-pytorch@master

      - name: Setup Linux
        uses: ./.github/actions/setup-linux

      - name: Calculate docker image
        id: calculate-docker-image
        uses: ./.github/actions/calculate-docker-image
        with:
          docker-image-name: ${{ inputs.docker-image-name }}
          xla: ${{ contains(inputs.build-environment, 'xla') }}

      - name: Pull docker image
        uses: pytorch/test-infra/.github/actions/pull-docker-image@main
        with:
          docker-image: ${{ steps.calculate-docker-image.outputs.docker-image }}

      - name: Output disk space left
        run: |
          sudo df -H

      - name: Preserve github env variables for use in docker
        run: |
          env | grep '^GITHUB' >> "/tmp/github_env_${GITHUB_RUN_ID}"
          env | grep '^CI' >> "/tmp/github_env_${GITHUB_RUN_ID}"

      - name: Build
        env:
          BUILD_ENVIRONMENT: ${{ inputs.build-environment }}
          TORCH_CUDA_ARCH_LIST: 5.2
          SCCACHE_BUCKET: ossci-compiler-cache-circleci-v2
          DOCKER_IMAGE: ${{ steps.calculate-docker-image.outputs.docker-image }}
        run: |
          set -e
          # Unlike other gradle jobs, it's not worth building libtorch in a separate CI job and share via docker, because:
          # 1) Not shareable: it's custom selective build, which is different from default libtorch mobile build;
          # 2) Not parallelizable by architecture: it only builds libtorch for one architecture;

          export BUILD_LITE_INTERPRETER
          BUILD_LITE_INTERPRETER="1"
          if [[ "${BUILD_ENVIRONMENT}" == *"full-jit" ]]; then
            BUILD_LITE_INTERPRETER="0"
          fi

          git submodule sync && git submodule update -q --init --recursive --depth 1
          export id
          id=$(docker run -e BUILD_ENVIRONMENT \
            -e MAX_JOBS="$(nproc --ignore=2)" \
            -e SCCACHE_BUCKET \
            -e SKIP_SCCACHE_INITIALIZATION=1 \
            -e TORCH_CUDA_ARCH_LIST \
            -e BUILD_LITE_INTERPRETER \
            --env-file="/tmp/github_env_${GITHUB_RUN_ID}" \
            --security-opt seccomp=unconfined \
            --cap-add=SYS_PTRACE \
            --tty \
            --detach \
            --user jenkins \
            -v "$(pwd):/var/lib/jenkins/workspace" \
            --cap-add=SYS_PTRACE \
            --security-opt seccomp=unconfined \
            --cap-add=SYS_PTRACE \
            --security-opt seccomp=unconfined \
            -t -d -w /var/lib/jenkins "${DOCKER_IMAGE}")

          export COMMAND
          # shellcheck disable=SC2016
          COMMAND='(echo "sudo chown -R jenkins workspace && cd workspace && ./.circleci/scripts/build_android_gradle.sh" | docker exec -u jenkins -e BUILD_LITE_INTERPRETER -e GRADLE_OFFLINE=1 -i "$id" bash) 2>&1'
          echo "${COMMAND}" > ./command.sh && bash ./command.sh
          # Skip docker push as this job is purely for size analysis purpose.
          # Result binaries are already in `/home/<USER>/project/` as it's mounted instead of copied.

      - name: Chown workspace
        uses: ./.github/actions/chown-workspace
        if: always()

      - name: Teardown Linux
        uses: pytorch/test-infra/.github/actions/teardown-linux@main
        if: always()
