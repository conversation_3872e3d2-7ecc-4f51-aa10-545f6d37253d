name: update-commit-hash

on:
  workflow_call:
    inputs:
      repo-name:
        required: true
        type: string
        description: Name of the repository we're updating commit hash for.  Must be in pytorch org.
      branch:
        required: true
        type: string
        description: Branch to fetch commit of
    secrets:
      MERGEBOT_TOKEN:
        required: true
        description: Permissions for opening PR
      PYTORCHBOT_TOKEN:
        required: true
        description: Permissions for approving PR

env:
  NEW_BRANCH_NAME: update-${{ inputs.repo-name }}-commit-hash/${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }}

jobs:
  update-xla-commit-hash:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v3
        with:
          fetch-depth: 1
          submodules: false
          token: ${{ secrets.MERGEBOT_TOKEN }}

      - name: Checkout
        shell: bash
        run: |
          git clone https://github.com/pytorch/${{ inputs.repo-name }}.git --quiet

      - name: Check if there already exists a PR
        shell: bash
        env:
          REPO_NAME: ${{ inputs.repo-name }}
          BRANCH: ${{ inputs.branch }}
          MERGEBOT_TOKEN: ${{ secrets.MERGEBOT_TOKEN }}
          PYTORCHBOT_TOKEN: ${{ secrets.PYTORCHBOT_TOKEN }}
        run: |
          # put this here instead of the script to prevent accidentally changing the config when running the script locally
          git config --global user.name "PyTorch MergeBot"
          git config --global user.email "<EMAIL>"

          python .github/scripts/update_commit_hashes.py --repo-name "${REPO_NAME}" --branch "${BRANCH}"
