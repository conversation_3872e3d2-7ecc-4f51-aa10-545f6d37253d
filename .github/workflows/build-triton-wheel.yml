name: Build Triton wheels

on:
  push:
    branches:
      - main
      - master
    paths:
      - .github/workflows/build-triton-wheel.yml
      - .github/scripts/build_triton_wheel.py
      - .github/ci_commit_pins/triton.txt
  pull_request:
    paths:
      - .github/workflows/build-triton-wheel.yml
      - .github/scripts/build_triton_wheel.py
      - .github/ci_commit_pins/triton.txt

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.sha }}-${{ github.event_name == 'workflow_dispatch' }}
  cancel-in-progress: true

jobs:
  build-wheel:
    name: "Build Triton Wheel"
    runs-on: [self-hosted, linux.2xlarge]
    strategy:
      fail-fast: false
      matrix:
        py_vers: [ "3.8", "3.9", "3.10", "3.11" ]
    timeout-minutes: 40
    env:
      DOCKER_IMAGE: pytorch/manylinux-builder:cpu
      PY_VERS: ${{ matrix.py_vers }}
    steps:
      - name: Setup SSH (Click me for login details)
        uses: pytorch/test-infra/.github/actions/setup-ssh@main
        with:
          github-secret: ${{ secrets.GITHUB_TOKEN }}

      - name: Checkout PyTorch
        uses: pytorch/pytorch/.github/actions/checkout-pytorch@master
        with:
          submodules: false

      - name: Setup Linux
        uses: ./.github/actions/setup-linux

      - name: Pull Docker image
        uses: pytorch/test-infra/.github/actions/pull-docker-image@main
        with:
          docker-image: ${{ env.DOCKER_IMAGE }}

      - name: Build Triton wheel
        run: |
          set -x
          mkdir -p "${RUNNER_TEMP}/artifacts/"
          container_name=$(docker run \
            --tty \
            --detach \
            -v "${GITHUB_WORKSPACE}:/pytorch" \
            -v "${RUNNER_TEMP}/artifacts:/artifacts" \
            -w /artifacts/ \
            "${DOCKER_IMAGE}"      \
          )

          # Determine python executable for given version
          case $PY_VERS in
          3.8)
            PYTHON_EXECUTABLE=/opt/python/cp38-cp38/bin/python
            ;;
          3.9)
            PYTHON_EXECUTABLE=/opt/python/cp39-cp39/bin/python
            ;;
          3.10)
            PYTHON_EXECUTABLE=/opt/python/cp310-cp310/bin/python
            ;;
          3.11)
            PYTHON_EXECUTABLE=/opt/python/cp311-cp311/bin/python
            ;;
          *)
            echo "Unsupported python version ${PY_VERS}"
            exit 1
            ;;
          esac

          docker exec -t "${container_name}" yum install -y zlib-devel
          docker exec -t "${container_name}" "${PYTHON_EXECUTABLE}"  -m pip install -U setuptools==67.4.0
          docker exec -t "${container_name}" "${PYTHON_EXECUTABLE}" /pytorch/.github/scripts/build_triton_wheel.py
          docker exec -t "${container_name}" chown -R 1000.1000 /artifacts

      - uses: actions/upload-artifact@v3
        with:
          name: "pytorch-triton-wheel-${{ matrix.py_vers }}"
          if-no-files-found: error
          path:
            ${{ runner.temp }}/artifacts/*

      - name: Teardown Linux
        uses: pytorch/test-infra/.github/actions/teardown-linux@main
        if: always()
  upload-wheel:
    runs-on: linux.20_04.4x
    needs: build-wheel
    container:
      image: continuumio/miniconda3:4.12.0
    env:
      GITHUB_TOKEN: ${{ secrets.github-token }}
    steps:
      - name: Download Build Artifacts (3.8)
        uses: actions/download-artifact@v3
        with:
          name: "pytorch-triton-wheel-3.8"
          path: "${{ runner.temp }}/artifacts/"
      - name: Download Build Artifacts (3.9)
        uses: actions/download-artifact@v3
        with:
          name: "pytorch-triton-wheel-3.9"
          path: "${{ runner.temp }}/artifacts/"
      - name: Download Build Artifacts (3.10)
        uses: actions/download-artifact@v3
        with:
          name: "pytorch-triton-wheel-3.10"
          path: "${{ runner.temp }}/artifacts/"
      - name: Download Build Artifacts (3.11)
        uses: actions/download-artifact@v3
        with:
          name: "pytorch-triton-wheel-3.11"
          path: "${{ runner.temp }}/artifacts/"
      - name: Upload binaries
        if: ${{ github.event_name == 'push' && (github.event.ref == 'refs/heads/master' || github.event.ref == 'refs/heads/main') }}
        env:
          PKG_DIR: "${{ runner.temp }}/artifacts"
          # When running these on pull_request events these should be blank
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_S3_UPDATE_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_S3_UPDATE_SECRET_ACCESS_KEY }}
          UPLOAD_BUCKET: "s3://pytorch"
        run: |
            set -ex
            pip install -q awscli
            s3_dir="${UPLOAD_BUCKET}/whl/test/"
            for pkg in "${PKG_DIR}/"*.whl; do
              aws s3 cp --no-progress --acl public-read "${pkg}" "${s3_dir}"
             done
  build-conda:
    name: "Build Triton Conda"
    runs-on: [self-hosted, linux.2xlarge]
    strategy:
      fail-fast: false
      matrix:
        py_vers: [ "3.8", "3.9", "3.10", "3.11" ]
    timeout-minutes: 40
    env:
      DOCKER_IMAGE: pytorch/conda-builder:cpu
      PY_VERS: ${{ matrix.py_vers }}
      ANACONDA_API_TOKEN: ${{ secrets.CONDA_PYTORCHBOT_TOKEN }}
    steps:
      - name: Setup SSH (Click me for login details)
        uses: pytorch/test-infra/.github/actions/setup-ssh@main
        with:
          github-secret: ${{ secrets.GITHUB_TOKEN }}

      - name: Checkout PyTorch
        uses: pytorch/pytorch/.github/actions/checkout-pytorch@master
        with:
          submodules: false

      - name: Setup Linux
        uses: ./.github/actions/setup-linux

      - name: Pull Docker image
        uses: pytorch/test-infra/.github/actions/pull-docker-image@main
        with:
          docker-image: ${{ env.DOCKER_IMAGE }}

      - name: Build Triton conda package
        run: |
          set -x
          mkdir -p "${RUNNER_TEMP}/artifacts/"
          container_name=$(docker run \
            --tty \
            --detach \
            -v "${GITHUB_WORKSPACE}:/pytorch" \
            -v "${RUNNER_TEMP}/artifacts:/artifacts" \
            -w /artifacts/ \
            -e ANACONDA_API_TOKEN \
            "${DOCKER_IMAGE}" \
          )

          docker exec -t "${container_name}" yum install -y llvm11 llvm11-devel llvm11-static llvm11-libs zlib-devel
          docker exec -t "${container_name}" python /pytorch/.github/scripts/build_triton_wheel.py --build-conda --py-version="${PY_VERS}"

      - name: Upload artifacts to Anaconda
        if: ${{ github.event_name == 'push' && (github.event.ref == 'refs/heads/master' || github.event.ref == 'refs/heads/main') }}
        run: |
          container_name=$(docker container ps --format '{{.ID}}')
          docker exec -t "${container_name}" sh -c "anaconda upload /artifacts/torch*.tar.bz2 -u pytorch-test --label main --no-progress --force"

      - name: Chown artifacts
        run: |
          container_name=$(docker container ps --format '{{.ID}}')
          docker exec -t "${container_name}" chown -R 1000.1000 /artifacts

      - uses: actions/upload-artifact@v3
        with:
          name: "pytorch-triton-conda-${{ matrix.py_vers }}"
          if-no-files-found: error
          path:
            ${{ runner.temp }}/artifacts/*

      - name: Teardown Linux
        uses: pytorch/test-infra/.github/actions/teardown-linux@main
        if: always()
