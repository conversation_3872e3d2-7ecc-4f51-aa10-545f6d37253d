name: calculate-docker-image

on:
  workflow_call:
    inputs:
      docker-image-name:
        required: true
        type: string
        description: Name of the base docker image to build with.

    outputs:
      docker-image:
        value: ${{ jobs.calculate-docker-image.outputs.docker-image }}
        description: The docker image containing the built PyTorch.

jobs:
  calculate-docker-image:
    if: github.repository_owner == 'pytorch'
    runs-on: [self-hosted, linux.large]
    timeout-minutes: 15
    outputs:
      docker-image: ${{ steps.calculate-docker-image.outputs.docker-image }}
    steps:
      - name: Checkout PyTorch
        uses: pytorch/pytorch/.github/actions/checkout-pytorch@master
        with:
          submodules: false
          fetch-depth: 1

      - name: Setup Linux
        uses: ./.github/actions/setup-linux

      - name: Calculate docker image
        id: calculate-docker-image
        uses: ./.github/actions/calculate-docker-image
        with:
          docker-image-name: ${{ inputs.docker-image-name }}
