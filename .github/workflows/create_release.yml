name: Create Release

on:
  push:
    tags: ['v*']
    branches:
      - master
      - main
      - nightly
  release:
    types: [published]
  pull_request:
    paths: [.github/workflows/create_release.yml]

jobs:
  release:
    if: ${{ github.repository == 'pytorch/pytorch' }}
    name: Create Release
    runs-on: ubuntu-latest
    steps:
      - uses: malfet/checkout@silent-checkout
        with:
          submodules: 'recursive'
          ref: ${{ github.event_name == 'pull_request' && github.event.pull_request.head.sha || github.sha }}
      - name: Fake name for PRs
        if: ${{ github.event_name == 'pull_request' }}
        run: echo "PT_GITHUB_REF=refs/tags/pr-tag" >> "$GITHUB_ENV"
      - name: Real name for non-PRs
        if: ${{ github.event_name != 'pull_request' }}
        run: echo "PT_GITHUB_REF=$GITHUB_REF" >> "$GITHUB_ENV"
      - name: Set filenames
        run: |
          tag_or_branch="${PT_GITHUB_REF#refs/tags/}"
          tag_or_branch="${tag_or_branch#refs/heads/}"
          echo "PT_RELEASE_NAME=pytorch-$tag_or_branch" >> "$GITHUB_ENV"
          echo "PT_RELEASE_FILE=pytorch-$tag_or_branch.tar.gz" >> "$GITHUB_ENV"
      - name: Create source distribution
        run: |
            # Create new folder with specified name so extracting the archive yields that
            rm -rf "/tmp/$PT_RELEASE_NAME"
            cp -r "$PWD" "/tmp/$PT_RELEASE_NAME"
            mv "/tmp/$PT_RELEASE_NAME" .
            # Cleanup
            rm -rf "$PT_RELEASE_NAME"/{.circleci,.ci}
            find "$PT_RELEASE_NAME" -name '.git*' -exec rm -rv {} \; || true
            # Create archive
            tar -czf "$PT_RELEASE_FILE" "$PT_RELEASE_NAME"
            echo "Created source archive $PT_RELEASE_FILE with content: $(ls -a "$PT_RELEASE_NAME")"
      - name: Upload source distribution
        if: ${{ github.event_name == 'release' }}
        uses: softprops/action-gh-release@v1
        with:
          files: ${{env.PT_RELEASE_FILE}}

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.sha }}-${{ github.event_name == 'workflow_dispatch' }}
  cancel-in-progress: true
