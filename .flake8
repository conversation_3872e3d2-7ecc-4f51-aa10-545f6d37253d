[flake8]
select = B,C,E,F,P,T4,W,B9
max-line-length = 120
# C408 ignored because we like the dict keyword argument syntax
# E501 is not flexible enough, we're using B950 instead
ignore =
    E203,E305,E402,E501,E721,E741,F405,F821,F841,F999,W503,W504,C408,E302,W291,E303,
    # shebang has extra meaning in fbcode lints, so I think it's not worth trying
    # to line this up with executable bit
    EXE001,
    # these ignores are from flake8-bugbear; please fix!
    B007,B008,
    # these ignores are from flake8-comprehensions; please fix!
    C407
per-file-ignores =
    __init__.py: F401
    torch/utils/cpp_extension.py: B950
    torchgen/api/types/__init__.py: F401,F403
    torchgen/executorch/api/types/__init__.py: F401,F403
optional-ascii-coding = True
exclude =
    ./.git,
    ./build_test_custom_build,
    ./build,
    ./caffe2,
    ./docs/caffe2,
    ./docs/cpp/src,
    ./docs/src,
    ./functorch/docs,
    ./functorch/examples,
    ./functorch/notebooks,
    ./scripts,
    ./test/generated_type_hints_smoketest.py,
    ./third_party,
    ./torch/include,
    ./torch/lib,
    ./venv,
    *.pyi
