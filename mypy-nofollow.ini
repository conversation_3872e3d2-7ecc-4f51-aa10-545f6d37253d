[mypy]
plugins = mypy_plugins/check_mypy_version.py

cache_dir = .mypy_cache/nofollow
warn_unused_configs = True
warn_redundant_casts = True
show_error_codes = True
show_column_numbers = True
check_untyped_defs = True
follow_imports = skip

# do not reenable this:
# https://github.com/pytorch/pytorch/pull/60006#issuecomment-866130657
warn_unused_ignores = False
disallow_any_generics = True

files =
    torch/_dynamo,
    test/test_utils.py

# Minimum version supported - variable annotations were introduced
# in Python 3.8
python_version = 3.8

[mypy-sympy]
ignore_missing_imports = True

[mypy-sympy.*]
ignore_missing_imports = True

[mypy-torch._C]
ignore_errors = True

[mypy-torch._C.*]
ignore_errors = True
