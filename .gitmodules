[submodule "third_party/pybind11"]
    ignore = dirty
    path = third_party/pybind11
    url = https://github.com/pybind/pybind11.git
[submodule "third_party/cub"]
    ignore = dirty
    path = third_party/cub
    url = https://github.com/NVlabs/cub.git
[submodule "third_party/eigen"]
    ignore = dirty
    path = third_party/eigen
    url = https://gitlab.com/libeigen/eigen.git
[submodule "third_party/googletest"]
    ignore = dirty
    path = third_party/googletest
    url = https://github.com/google/googletest.git
[submodule "third_party/benchmark"]
    ignore = dirty
    path = third_party/benchmark
    url = https://github.com/google/benchmark.git
[submodule "third_party/protobuf"]
    ignore = dirty
    path = third_party/protobuf
    url = https://github.com/protocolbuffers/protobuf.git
[submodule "third_party/ios-cmake"]
    ignore = dirty
    path = third_party/ios-cmake
    url = https://github.com/Yangqing/ios-cmake.git
[submodule "third_party/NNPACK"]
    ignore = dirty
    path = third_party/NNPACK
    url = https://github.com/Maratyszcza/NNPACK.git
[submodule "third_party/gloo"]
    ignore = dirty
    path = third_party/gloo
    url = https://github.com/facebookincubator/gloo
[submodule "third_party/NNPACK_deps/pthreadpool"]
    ignore = dirty
    path = third_party/pthreadpool
    url = https://github.com/Maratyszcza/pthreadpool.git
[submodule "third_party/NNPACK_deps/FXdiv"]
    ignore = dirty
    path = third_party/FXdiv
    url = https://github.com/Maratyszcza/FXdiv.git
[submodule "third_party/NNPACK_deps/FP16"]
    ignore = dirty
    path = third_party/FP16
    url = https://github.com/Maratyszcza/FP16.git
[submodule "third_party/NNPACK_deps/psimd"]
    ignore = dirty
    path = third_party/psimd
    url = https://github.com/Maratyszcza/psimd.git
[submodule "third_party/zstd"]
    ignore = dirty
    path = third_party/zstd
    url = https://github.com/facebook/zstd.git
[submodule "third_party/cpuinfo"]
    ignore = dirty
    path = third_party/cpuinfo
    url = https://github.com/pytorch/cpuinfo.git
[submodule "third_party/python-enum"]
    ignore = dirty
    path = third_party/python-enum
    url = https://github.com/PeachPy/enum34.git
[submodule "third_party/python-peachpy"]
    ignore = dirty
    path = third_party/python-peachpy
    url = https://github.com/malfet/PeachPy.git
[submodule "third_party/python-six"]
    ignore = dirty
    path = third_party/python-six
    url = https://github.com/benjaminp/six.git
[submodule "third_party/onnx"]
    ignore = dirty
    path = third_party/onnx
    url = https://github.com/onnx/onnx.git
[submodule "third_party/onnx-tensorrt"]
    ignore = dirty
    path = third_party/onnx-tensorrt
    url = https://github.com/onnx/onnx-tensorrt
[submodule "third_party/sleef"]
    ignore = dirty
    path = third_party/sleef
    url = https://github.com/shibatch/sleef
[submodule "third_party/ideep"]
    ignore = dirty
    path = third_party/ideep
    url = https://github.com/intel/ideep
[submodule "third_party/nccl/nccl"]
    ignore = dirty
    path = third_party/nccl/nccl
    url = https://github.com/NVIDIA/nccl
[submodule "third_party/gemmlowp/gemmlowp"]
    ignore = dirty
    path = third_party/gemmlowp/gemmlowp
    url = https://github.com/google/gemmlowp.git
[submodule "third_party/QNNPACK"]
    ignore = dirty
    path = third_party/QNNPACK
    url = https://github.com/pytorch/QNNPACK
[submodule "third_party/neon2sse"]
    ignore = dirty
    path = third_party/neon2sse
    url = https://github.com/intel/ARM_NEON_2_x86_SSE.git
[submodule "third_party/fbgemm"]
    ignore = dirty
    path = third_party/fbgemm
    url = https://github.com/pytorch/fbgemm
[submodule "third_party/foxi"]
    ignore = dirty
    path = third_party/foxi
    url = https://github.com/houseroad/foxi.git
[submodule "third_party/tbb"]
    path = third_party/tbb
    url = https://github.com/01org/tbb
    branch = tbb_2018
[submodule "android/libs/fbjni"]
    ignore = dirty
    path = android/libs/fbjni
    url = https://github.com/facebookincubator/fbjni.git
[submodule "third_party/XNNPACK"]
    ignore = dirty
    path = third_party/XNNPACK
    url = https://github.com/google/XNNPACK.git
[submodule "third_party/fmt"]
    ignore = dirty
    path = third_party/fmt
    url = https://github.com/fmtlib/fmt.git
[submodule "third_party/tensorpipe"]
    ignore = dirty
    path = third_party/tensorpipe
    url = https://github.com/pytorch/tensorpipe.git
[submodule "third_party/cudnn_frontend"]
	path = third_party/cudnn_frontend
	url = https://github.com/NVIDIA/cudnn-frontend.git
[submodule "third_party/kineto"]
    path = third_party/kineto
    url = https://github.com/pytorch/kineto
[submodule "third_party/pocketfft"]
	path = third_party/pocketfft
	url = https://github.com/mreineck/pocketfft
[submodule "third_party/ittapi"]
	path = third_party/ittapi
	url = https://github.com/intel/ittapi.git
[submodule "third_party/flatbuffers"]
	path = third_party/flatbuffers
	url = https://github.com/google/flatbuffers.git
[submodule "third_party/nlohmann"]
	path = third_party/nlohmann
	url = https://github.com/nlohmann/json.git
[submodule "third_party/VulkanMemoryAllocator"]
	path = third_party/VulkanMemoryAllocator
	url = https://github.com/GPUOpen-LibrariesAndSDKs/VulkanMemoryAllocator.git
[submodule "third_party/cutlass"]
	path = third_party/cutlass
	url = https://github.com/NVIDIA/cutlass.git
