[pt]
  is_oss=1

[buildfile]
  name = BUCK.oss
  includes = //tools/build_defs/select.bzl

[repositories]
  bazel_skylib = third_party/bazel-skylib/
  ovr_config = .

[download]
  in_build = true

[cxx]
  cxxflags = -std=c++17
  should_remap_host_platform = true
  cpp = /usr/bin/clang
  cc = /usr/bin/clang
  cxx = /usr/bin/clang++
  cxxpp = /usr/bin/clang++
  ld = /usr/bin/clang++

[project]
  default_flavors_mode=all
