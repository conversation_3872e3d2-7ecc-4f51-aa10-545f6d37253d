load("@bazel_skylib//lib:paths.bzl", "paths")
load("@pybind11_bazel//:build_defs.bzl", "pybind_extension")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@rules_cc//cc:defs.bzl", "cc_binary", "cc_library", "cc_proto_library", "cc_test")
load("@pytorch//third_party:substitution.bzl", "header_template_rule")
load("@pytorch//:tools/bazel.bzl", "rules")
load("@pytorch//tools/rules:cu.bzl", "cu_library")
load("@pytorch//tools/config:defs.bzl", "if_cuda")
load("@pytorch//:aten.bzl", "intern_build_aten_ops", "generate_aten")
load(":build.bzl", "define_targets", "GENERATED_AUTOGRAD_CPP", "GENERATED_AUTOGRAD_PYTHON")
load(":build_variables.bzl", "jit_core_sources", "libtorch_core_sources", "libtorch_cuda_sources", "libtorch_distributed_sources", "libtorch_extra_sources", "libtorch_nvfuser_generated_headers", "libtorch_nvfuser_runtime_sources", "libtorch_python_core_sources", "torch_cpp_srcs", "lazy_tensor_ts_sources")
load(":ufunc_defs.bzl", "aten_ufunc_generated_cpu_sources", "aten_ufunc_generated_cpu_kernel_sources", "aten_ufunc_generated_cuda_sources")

define_targets(rules = rules)

COMMON_COPTS = [
    "-DHAVE_MALLOC_USABLE_SIZE=1",
    "-DHAVE_MMAP=1",
    "-DHAVE_SHM_OPEN=1",
    "-DHAVE_SHM_UNLINK=1",
    "-D_FILE_OFFSET_BITS=64",
    "-DTH_HAVE_THREAD",
    "-DUSE_FBGEMM",
    "-DUSE_DISTRIBUTED",
    "-DAT_PER_OPERATOR_HEADERS",
    "-DATEN_THREADING=NATIVE",
    "-DNO_CUDNN_DESTROY_HANDLE",
] + if_cuda([
    "-DUSE_CUDA",
    "-DUSE_CUDNN",
    # TODO: This should be passed only when building for CUDA-11.5 or newer
    # use cub in a safe manner, see:
    # https://github.com/pytorch/pytorch/pull/55292
    "-DCUB_WRAPPED_NAMESPACE=at_cuda_detail",
])

aten_generation_srcs = ["aten/src/ATen/native/native_functions.yaml"] + ["aten/src/ATen/native/tags.yaml"] + glob(["aten/src/ATen/templates/**"])

generated_cpu_cpp = [
    "aten/src/ATen/RegisterBackendSelect.cpp",
    "aten/src/ATen/RegisterCPU.cpp",
    "aten/src/ATen/RegisterFunctionalization_0.cpp",
    "aten/src/ATen/RegisterFunctionalization_1.cpp",
    "aten/src/ATen/RegisterFunctionalization_2.cpp",
    "aten/src/ATen/RegisterFunctionalization_3.cpp",
    # "aten/src/ATen/RegisterFunctionalizationEverything.cpp",
    "aten/src/ATen/RegisterMkldnnCPU.cpp",
    "aten/src/ATen/RegisterNestedTensorCPU.cpp",
    "aten/src/ATen/RegisterQuantizedCPU.cpp",
    "aten/src/ATen/RegisterSparseCPU.cpp",
    "aten/src/ATen/RegisterSparseCsrCPU.cpp",
    "aten/src/ATen/RegisterZeroTensor.cpp",
    "aten/src/ATen/RegisterCompositeImplicitAutograd.cpp",
    "aten/src/ATen/RegisterCompositeImplicitAutogradNestedTensor.cpp",
    "aten/src/ATen/RegisterCompositeExplicitAutograd.cpp",
    "aten/src/ATen/RegisterCompositeExplicitAutogradNonFunctional.cpp",
    "aten/src/ATen/RegisterMeta.cpp",
    "aten/src/ATen/RegisterSparseMeta.cpp",
    "aten/src/ATen/RegisterQuantizedMeta.cpp",
    "aten/src/ATen/RegisterNestedTensorMeta.cpp",
    "aten/src/ATen/RegisterSchema.cpp",
    "aten/src/ATen/CPUFunctions.h",
    "aten/src/ATen/CPUFunctions_inl.h",
    "aten/src/ATen/CompositeExplicitAutogradFunctions.h",
    "aten/src/ATen/CompositeExplicitAutogradFunctions_inl.h",
    "aten/src/ATen/CompositeExplicitAutogradNonFunctionalFunctions.h",
    "aten/src/ATen/CompositeExplicitAutogradNonFunctionalFunctions_inl.h",
    "aten/src/ATen/CompositeImplicitAutogradFunctions.h",
    "aten/src/ATen/CompositeImplicitAutogradFunctions_inl.h",
    "aten/src/ATen/CompositeImplicitAutogradNestedTensorFunctions.h",
    "aten/src/ATen/CompositeImplicitAutogradNestedTensorFunctions_inl.h",
    "aten/src/ATen/CompositeViewCopyKernels.cpp",
    "aten/src/ATen/FunctionalInverses.h",
    "aten/src/ATen/Functions.h",
    "aten/src/ATen/Functions.cpp",
    "aten/src/ATen/RedispatchFunctions.h",
    "aten/src/ATen/Operators.h",
    "aten/src/ATen/Operators_0.cpp",
    "aten/src/ATen/Operators_1.cpp",
    "aten/src/ATen/Operators_2.cpp",
    "aten/src/ATen/Operators_3.cpp",
    "aten/src/ATen/Operators_4.cpp",
    "aten/src/ATen/NativeFunctions.h",
    "aten/src/ATen/MetaFunctions.h",
    "aten/src/ATen/MetaFunctions_inl.h",
    "aten/src/ATen/MethodOperators.h",
    "aten/src/ATen/NativeMetaFunctions.h",
    "aten/src/ATen/RegistrationDeclarations.h",
    "aten/src/ATen/VmapGeneratedPlumbing.h",
    "aten/src/ATen/core/aten_interned_strings.h",
    "aten/src/ATen/core/enum_tag.h",
    "aten/src/ATen/core/TensorBody.h",
    "aten/src/ATen/core/TensorMethods.cpp",
    "aten/src/ATen/core/ATenOpList.cpp",
]

generated_cuda_cpp = [
    "aten/src/ATen/CUDAFunctions.h",
    "aten/src/ATen/CUDAFunctions_inl.h",
    "aten/src/ATen/RegisterCUDA.cpp",
    "aten/src/ATen/RegisterNestedTensorCUDA.cpp",
    "aten/src/ATen/RegisterQuantizedCUDA.cpp",
    "aten/src/ATen/RegisterSparseCUDA.cpp",
    "aten/src/ATen/RegisterSparseCsrCUDA.cpp",
]

generate_aten(
    name = "generated_aten_cpp",
    srcs = aten_generation_srcs,
    outs = (
        generated_cpu_cpp +
        generated_cuda_cpp +
        aten_ufunc_generated_cpu_sources("aten/src/ATen/{}") +
        aten_ufunc_generated_cpu_kernel_sources("aten/src/ATen/{}") +
        aten_ufunc_generated_cuda_sources("aten/src/ATen/{}") +
        ["aten/src/ATen/Declarations.yaml"]
    ),
    generator = "//torchgen:gen",
)

filegroup(
    name = "cpp_generated_code",
    data = [":generate-code"],
    srcs = GENERATED_AUTOGRAD_CPP,
)

exports_files(
    srcs = ["aten/src/ATen/cpu/tbb/extra/version_string.ver.in"],
)

# ATen
filegroup(
    name = "aten_base_cpp",
    srcs = glob([
        "aten/src/ATen/*.cpp",
        "aten/src/ATen/functorch/*.cpp",
        "aten/src/ATen/detail/*.cpp",
        "aten/src/ATen/cpu/*.cpp",
    ]),
)

filegroup(
    name = "ATen_CORE_SRCS",
    srcs = glob(
        [
            "aten/src/ATen/core/**/*.cpp",
        ],
        exclude = [
            "aten/src/ATen/core/**/*_test.cpp",
        ],
    ),
)

filegroup(
    name = "aten_native_cpp",
    srcs = glob(["aten/src/ATen/native/*.cpp"]),
)

filegroup(
    name = "aten_native_sparse_cpp",
    srcs = glob(["aten/src/ATen/native/sparse/*.cpp"]),
)

filegroup(
    name = "aten_native_nested_cpp",
    srcs = glob(["aten/src/ATen/native/nested/*.cpp"]),
)

filegroup(
    name = "aten_native_quantized_cpp",
    srcs = glob(
        [
            "aten/src/ATen/native/quantized/*.cpp",
            "aten/src/ATen/native/quantized/cpu/*.cpp",
        ],
    ),
)

filegroup(
    name = "aten_native_transformers_cpp",
    srcs = glob(["aten/src/ATen/native/transformers/*.cpp"]),
)

filegroup(
    name = "aten_native_mkl_cpp",
    srcs = glob(["aten/src/ATen/native/mkl/*.cpp", "aten/src/ATen/mkl/*.cpp"]),
)

filegroup(
    name = "aten_native_mkldnn_cpp",
    srcs = glob(["aten/src/ATen/native/mkldnn/*.cpp"]),
)

filegroup(
    name = "aten_native_xnnpack",
    srcs = glob(["aten/src/ATen/native/xnnpack/*.cpp"]),
)

filegroup(
    name = "aten_base_vulkan",
    srcs = glob(["aten/src/ATen/vulkan/*.cpp"]),
)

filegroup(
    name = "aten_base_metal",
    srcs = glob(["aten/src/ATen/metal/*.cpp"]),
)

filegroup(
    name = "ATen_QUANTIZED_SRCS",
    srcs = glob(
        [
            "aten/src/ATen/quantized/**/*.cpp",
        ],
        exclude = [
            "aten/src/ATen/quantized/**/*_test.cpp",
        ],
    ),
)

filegroup(
    name = "aten_cuda_cpp_srcs",
    srcs = glob(
        [
            "aten/src/ATen/cuda/*.cpp",
            "aten/src/ATen/cuda/detail/*.cpp",
            "aten/src/ATen/cudnn/*.cpp",
            "aten/src/ATen/native/cuda/*.cpp",
            "aten/src/ATen/native/cuda/linalg/*.cpp",
            "aten/src/ATen/native/cudnn/*.cpp",
            "aten/src/ATen/native/miopen/*.cpp",
            "aten/src/ATen/native/nested/cuda/*.cpp",
            "aten/src/ATen/native/quantized/cuda/*.cpp",
            "aten/src/ATen/native/quantized/cudnn/*.cpp",
            "aten/src/ATen/native/sparse/cuda/*.cpp",
            "aten/src/ATen/native/transformers/cuda/*.cpp",
        ],
    ),
)

filegroup(
    name = "aten_cu_srcs",
    srcs = glob([
        "aten/src/ATen/cuda/*.cu",
        "aten/src/ATen/cuda/detail/*.cu",
        "aten/src/ATen/native/cuda/*.cu",
        "aten/src/ATen/native/nested/cuda/*.cu",
        "aten/src/ATen/native/quantized/cuda/*.cu",
        "aten/src/ATen/native/sparse/cuda/*.cu",
        "aten/src/ATen/native/transformers/cuda/*.cu",
    ]) + aten_ufunc_generated_cuda_sources("aten/src/ATen/{}"),
    # It's a bit puzzling to me why it's not necessary to declare the
    # target that generates these sources...
)

header_template_rule(
    name = "aten_src_ATen_config",
    src = "aten/src/ATen/Config.h.in",
    out = "aten/src/ATen/Config.h",
    include = "aten/src",
    substitutions = {
        "@AT_MKLDNN_ENABLED@": "1",
        "@AT_MKL_ENABLED@": "1",
        "@AT_MKL_SEQUENTIAL@": "0",
        "@AT_FFTW_ENABLED@": "0",
        "@AT_POCKETFFT_ENABLED@": "0",
        "@AT_NNPACK_ENABLED@": "0",
        "@CAFFE2_STATIC_LINK_CUDA_INT@": "0",
        "@AT_BUILD_WITH_BLAS@": "1",
        "@AT_BUILD_WITH_LAPACK@": "1",
        "@AT_PARALLEL_OPENMP@": "0",
        "@AT_PARALLEL_NATIVE@": "1",
        "@AT_PARALLEL_NATIVE_TBB@": "0",
        "@AT_BLAS_F2C@": "0",
        "@AT_BLAS_USE_CBLAS_DOT@": "1",
    },
)

header_template_rule(
    name = "aten_src_ATen_cuda_config",
    src = "aten/src/ATen/cuda/CUDAConfig.h.in",
    out = "aten/src/ATen/cuda/CUDAConfig.h",
    include = "aten/src",
    substitutions = {
        "@AT_CUDNN_ENABLED@": "1",
        "@AT_ROCM_ENABLED@": "0",
        "@AT_MAGMA_ENABLED@": "0",
        "@NVCC_FLAGS_EXTRA@": "",
    },
)

cc_library(
    name = "aten_headers",
    hdrs = [
        "torch/csrc/Export.h",
        "torch/csrc/jit/frontend/function_schema_parser.h",
    ] + glob([
        "aten/src/**/*.h",
        "aten/src/**/*.hpp",
        "aten/src/ATen/cuda/**/*.cuh",
        "aten/src/ATen/native/**/*.cuh",
        "aten/src/THC/*.cuh",
    ],
    ) + [
        ":aten_src_ATen_config",
        ":generated_aten_cpp",
    ],
    includes = [
        "aten/src",
    ],
    deps = [
        "//c10:headers",
    ],
)

ATEN_COPTS = COMMON_COPTS + [
    "-DCAFFE2_BUILD_MAIN_LIBS",
    "-DHAVE_AVX_CPU_DEFINITION",
    "-DHAVE_AVX2_CPU_DEFINITION",
    "-fvisibility-inlines-hidden",
    "-fno-math-errno",
    "-fno-trapping-math",
]

intern_build_aten_ops(
    copts = ATEN_COPTS,
    deps = [
        ":aten_headers",
        "@sleef",
        "@fbgemm",
        "@mkl",
    ],
    extra_impls = aten_ufunc_generated_cpu_kernel_sources("aten/src/ATen/{}"),
)

cc_library(
    name = "aten",
    srcs = [
        ":ATen_CORE_SRCS",
        ":ATen_QUANTIZED_SRCS",
        ":aten_base_cpp",
        ":aten_base_metal",
        ":aten_base_vulkan",
        ":aten_native_cpp",
        ":aten_native_mkl_cpp",
        ":aten_native_mkldnn_cpp",
        ":aten_native_quantized_cpp",
        ":aten_native_sparse_cpp",
        ":aten_native_nested_cpp",
        ":aten_native_transformers_cpp",
        ":aten_native_xnnpack",
        ":aten_src_ATen_config",
    ] + generated_cpu_cpp + aten_ufunc_generated_cpu_sources("aten/src/ATen/{}"),
    copts = ATEN_COPTS,
    data = if_cuda(
        [":libcaffe2_nvrtc.so"],
        [],
    ),
    visibility = ["//visibility:public"],
    deps = [
        ":ATen_CPU",
        ":aten_headers",
        ":caffe2_for_aten_headers",
        ":torch_headers",
        "@fbgemm",
        "@ideep",
    ],
    alwayslink = True,
)

cc_library(
    name = "aten_nvrtc",
    srcs = glob([
        "aten/src/ATen/cuda/nvrtc_stub/*.cpp",
    ]),
    copts = ATEN_COPTS,
    linkstatic = True,
    visibility = ["//visibility:public"],
    deps = [
        ":aten_headers",
        "@cuda",
        "@cuda//:cuda_driver",
        "@cuda//:nvrtc",
        "//c10:headers",
    ],
    alwayslink = True,
)

cc_binary(
    name = "libcaffe2_nvrtc.so",
    linkshared = True,
    visibility = ["//visibility:public"],
    deps = [
        ":aten_nvrtc",
    ],
)

cc_library(
    name = "aten_cuda_cpp",
    srcs = [":aten_cuda_cpp_srcs"] + generated_cuda_cpp,
    hdrs = [":aten_src_ATen_cuda_config"],
    copts = ATEN_COPTS,
    visibility = ["//visibility:public"],
    deps = [
        ":aten",
        "@cuda",
        "@cuda//:cusolver",
        "@cuda//:nvrtc",
        "@cudnn",
    ],
    alwayslink = True,
)

torch_cuda_half_options = [
    "-DCUDA_HAS_FP16=1",
    "-D__CUDA_NO_HALF_OPERATORS__",
    "-D__CUDA_NO_HALF_CONVERSIONS__",
    "-D__CUDA_NO_BFLOAT16_CONVERSIONS__",
    "-D__CUDA_NO_HALF2_OPERATORS__",
]

cu_library(
    name = "aten_cuda",
    srcs = [":aten_cu_srcs"],
    copts = ATEN_COPTS + torch_cuda_half_options,
    visibility = ["//visibility:public"],
    deps = [
        ":aten_cuda_cpp",
        "@cuda//:cublas",
        "@cuda//:cufft",
        "@cuda//:cusparse",
        "@cutlass",
    ],
    alwayslink = True,
)

# caffe2
CAFFE2_COPTS = COMMON_COPTS + [
    "-Dcaffe2_EXPORTS",
    "-DCAFFE2_USE_GLOO",
    "-DCAFFE2_USE_CUDNN",
    "-DCAFFE2_BUILD_MAIN_LIB",
    "-fvisibility-inlines-hidden",
    "-fno-math-errno",
    "-fno-trapping-math",
]

proto_library(
    name = "caffe2_proto_source",
    srcs = glob([
        "caffe2/proto/*.proto",
    ]),
    visibility = ["//visibility:public"],
)

cc_proto_library(
    name = "caffe2_protos",
    deps = [":caffe2_proto_source"],
)

header_template_rule(
    name = "caffe2_core_macros_h",
    src = "caffe2/core/macros.h.in",
    out = "caffe2/core/macros.h",
    substitutions = {
        "@CAFFE2_VERSION_MAJOR@": "1",
        "@CAFFE2_VERSION_MINOR@": "3",
        "@CAFFE2_VERSION_PATCH@": "0",
        "cmakedefine": "define",
        "#define CAFFE2_FORCE_FALLBACK_CUDA_MPI": "/* #undef CAFFE2_FORCE_FALLBACK_CUDA_MPI */",
        "#define CAFFE2_HAS_MKL_DNN": "/* #undef CAFFE2_HAS_MKL_DNN */",
        "#define CAFFE2_HAS_MKL_SGEMM_PACK": "/* #undef CAFFE2_HAS_MKL_SGEMM_PACK */",
        "#define CAFFE2_THREADPOOL_MAIN_IMBALANCE": "/* #undef CAFFE2_THREADPOOL_MAIN_IMBALANCE */",
        "#define CAFFE2_THREADPOOL_STATS": "/* #undef CAFFE2_THREADPOOL_STATS */",
        "#define CAFFE2_USE_ACCELERATE": "/* #undef CAFFE2_USE_ACCELERATE */",
        "#define CAFFE2_USE_EIGEN_FOR_BLAS": "/* #undef CAFFE2_USE_EIGEN_FOR_BLAS */",
        "#define CAFFE2_USE_FBCODE": "/* #undef CAFFE2_USE_FBCODE */",
        "#define CAFFE2_USE_GOOGLE_GLOG": "/* #undef CAFFE2_USE_GOOGLE_GLOG */",
        "#define CAFFE2_USE_LITE_PROTO": "/* #undef CAFFE2_USE_LITE_PROTO */",
        "#define CAFFE2_USE_MKL\n": "/* #undef CAFFE2_USE_MKL */\n",
        "#define CAFFE2_USE_NVTX": "/* #undef CAFFE2_USE_NVTX */",
        "#define CAFFE2_USE_TRT": "/* #undef CAFFE2_USE_TRT */",
    },
)

filegroup(
    name = "caffe2_contrib_srcs",
    srcs = [
        "caffe2/contrib/aten/aten_op.cc",
        "caffe2/contrib/gloo/allgather_ops.cc",
        "caffe2/contrib/gloo/allreduce_ops.cc",
        "caffe2/contrib/gloo/barrier_ops.cc",
        "caffe2/contrib/gloo/broadcast_ops.cc",
        "caffe2/contrib/gloo/common.cc",
        "caffe2/contrib/gloo/common_world_ops.cc",
        "caffe2/contrib/gloo/context.cc",
        "caffe2/contrib/gloo/reduce_scatter_ops.cc",
        "caffe2/contrib/gloo/store_handler.cc",
    ],
)

filegroup(
    name = "caffe2_core_srcs",
    srcs = [
        "caffe2/core/allocator.cc",
        "caffe2/core/blob_serialization.cc",
        "caffe2/core/blob_stats.cc",
        "caffe2/core/common.cc",
        "caffe2/core/context.cc",
        "caffe2/core/context_base.cc",
        "caffe2/core/db.cc",
        "caffe2/core/event.cc",
        "caffe2/core/export_c10_op_to_caffe2.cc",
        "caffe2/core/graph.cc",
        "caffe2/core/init.cc",
        "caffe2/core/init_denormals.cc",
        "caffe2/core/init_intrinsics_check.cc",
        "caffe2/core/init_omp.cc",
        "caffe2/core/int8_serialization.cc",
        "caffe2/core/memonger.cc",
        "caffe2/core/module.cc",
        "caffe2/core/net.cc",
        "caffe2/core/net_async_base.cc",
        "caffe2/core/net_async_scheduling.cc",
        "caffe2/core/net_async_task.cc",
        "caffe2/core/net_async_task_future.cc",
        "caffe2/core/net_async_task_graph.cc",
        "caffe2/core/net_async_tracing.cc",
        "caffe2/core/net_dag_utils.cc",
        "caffe2/core/net_parallel.cc",
        "caffe2/core/net_simple.cc",
        "caffe2/core/net_simple_refcount.cc",
        "caffe2/core/nomnigraph/Representations/NeuralNet.cc",
        "caffe2/core/nomnigraph/tests/test_util.cc",
        "caffe2/core/numa.cc",
        "caffe2/core/operator.cc",
        "caffe2/core/operator_schema.cc",
        "caffe2/core/plan_executor.cc",
        "caffe2/core/prof_dag_counters.cc",
        "caffe2/core/qtensor.cc",
        "caffe2/core/qtensor_serialization.cc",
        "caffe2/core/stats.cc",
        "caffe2/core/tensor.cc",
        "caffe2/core/tensor_int8.cc",
        "caffe2/core/test_utils.cc",
        "caffe2/core/transform.cc",
        "caffe2/core/types.cc",
        "caffe2/core/workspace.cc",
    ],
)

filegroup(
    name = "caffe2_distributed_srcs",
    srcs = [
        "caffe2/distributed/file_store_handler.cc",
        "caffe2/distributed/file_store_handler_op.cc",
        "caffe2/distributed/store_handler.cc",
        "caffe2/distributed/store_ops.cc",
    ],
)

filegroup(
    name = "caffe2_ideep_srcs",
    srcs = [
        "caffe2/ideep/operators/adam_op.cc",
        "caffe2/ideep/operators/channel_shuffle_op.cc",
        "caffe2/ideep/operators/concat_split_op.cc",
        "caffe2/ideep/operators/conv_op.cc",
        "caffe2/ideep/operators/conv_transpose_op.cc",
        "caffe2/ideep/operators/dropout_op.cc",
        "caffe2/ideep/operators/elementwise_sum_op.cc",
        "caffe2/ideep/operators/expand_squeeze_dims_op.cc",
        "caffe2/ideep/operators/fully_connected_op.cc",
        "caffe2/ideep/operators/local_response_normalization_op.cc",
        "caffe2/ideep/operators/momentum_sgd_op.cc",
        "caffe2/ideep/operators/operator_fallback_ideep.cc",
        "caffe2/ideep/operators/order_switch_ops.cc",
        "caffe2/ideep/operators/pool_op.cc",
        "caffe2/ideep/operators/quantization/int8_add_op.cc",
        "caffe2/ideep/operators/quantization/int8_conv_op.cc",
        "caffe2/ideep/operators/quantization/int8_dequantize_op.cc",
        "caffe2/ideep/operators/quantization/int8_fully_connected_op.cc",
        "caffe2/ideep/operators/quantization/int8_given_tensor_fill_op.cc",
        "caffe2/ideep/operators/quantization/int8_pool_op.cc",
        "caffe2/ideep/operators/quantization/int8_quantize_op.cc",
        "caffe2/ideep/operators/quantization/int8_relu_op.cc",
        "caffe2/ideep/operators/queue_ops.cc",
        "caffe2/ideep/operators/relu_op.cc",
        "caffe2/ideep/operators/reshape_op.cc",
        "caffe2/ideep/operators/shape_op.cc",
        "caffe2/ideep/operators/sigmoid_op.cc",
        "caffe2/ideep/operators/spatial_batch_norm_op.cc",
        "caffe2/ideep/operators/transpose_op.cc",
        "caffe2/ideep/operators/utility_ops.cc",
        "caffe2/ideep/utils/ideep_register.cc",
    ],
)

filegroup(
    name = "caffe2_onnx_srcs",
    srcs = [
        "caffe2/onnx/backend.cc",
        "caffe2/onnx/backend_rep.cc",
        "caffe2/onnx/device.cc",
        "caffe2/onnx/helper.cc",
        "caffe2/onnx/offline_tensor.cc",
        "caffe2/onnx/onnx_exporter.cc",
        "caffe2/onnx/onnxifi_graph_info.cc",
        "caffe2/onnx/onnxifi_init.cc",
    ],
)

filegroup(
    name = "caffe2_operators_srcs",
    srcs = [
        "caffe2/operators/abs_op.cc",
        "caffe2/operators/accumulate_op.cc",
        "caffe2/operators/accuracy_op.cc",
        "caffe2/operators/acos_op.cc",
        "caffe2/operators/affine_channel_op.cc",
        "caffe2/operators/alias_with_name.cc",
        "caffe2/operators/apmeter_op.cc",
        "caffe2/operators/arg_ops.cc",
        "caffe2/operators/asin_op.cc",
        "caffe2/operators/assert_op.cc",
        "caffe2/operators/atan_op.cc",
        "caffe2/operators/atomic_ops.cc",
        "caffe2/operators/batch_box_cox_op.cc",
        "caffe2/operators/batch_bucketize_op.cc",
        "caffe2/operators/batch_gather_ops.cc",
        "caffe2/operators/batch_matmul_op.cc",
        "caffe2/operators/batch_moments_op.cc",
        "caffe2/operators/batch_permutation_op.cc",
        "caffe2/operators/batch_sparse_to_dense_op.cc",
        "caffe2/operators/bbox_transform_op.cc",
        "caffe2/operators/bisect_percentile_op.cc",
        "caffe2/operators/boolean_mask_ops.cc",
        "caffe2/operators/boolean_unmask_ops.cc",
        "caffe2/operators/box_with_nms_limit_op.cc",
        "caffe2/operators/bucketize_op.cc",
        "caffe2/operators/byte_weight_dequant_op.cc",
        "caffe2/operators/cast_op.cc",
        "caffe2/operators/cbrt_op.cc",
        "caffe2/operators/cc_bmm_bg_op.cc",
        "caffe2/operators/ceil_op.cc",
        "caffe2/operators/channel_backprop_stats_op.cc",
        "caffe2/operators/channel_shuffle_op.cc",
        "caffe2/operators/channel_stats_op.cc",
        "caffe2/operators/clip_op.cc",
        "caffe2/operators/collect_and_distribute_fpn_rpn_proposals_op.cc",
        "caffe2/operators/communicator_op.cc",
        "caffe2/operators/concat_split_op.cc",
        "caffe2/operators/conditional_op.cc",
        "caffe2/operators/conv_gradient_op.cc",
        "caffe2/operators/conv_op.cc",
        "caffe2/operators/conv_op_eigen.cc",
        "caffe2/operators/conv_op_shared.cc",
        "caffe2/operators/conv_transpose_gradient_op.cc",
        "caffe2/operators/conv_transpose_op.cc",
        "caffe2/operators/conv_transpose_op_mobile.cc",
        "caffe2/operators/copy_op.cc",
        "caffe2/operators/copy_rows_to_tensor_op.cc",
        "caffe2/operators/cos_op.cc",
        "caffe2/operators/cosh_op.cc",
        "caffe2/operators/cosine_embedding_criterion_op.cc",
        "caffe2/operators/counter_ops.cc",
        "caffe2/operators/crash_op.cc",
        "caffe2/operators/create_scope_op.cc",
        "caffe2/operators/crf_viterbi_op.cc",
        "caffe2/operators/cross_entropy_op.cc",
        "caffe2/operators/ctc_beam_search_decoder_op.cc",
        "caffe2/operators/ctc_greedy_decoder_op.cc",
        "caffe2/operators/cube_op.cc",
        "caffe2/operators/data_couple.cc",
        "caffe2/operators/dataset_ops.cc",
        "caffe2/operators/deform_conv_gradient_op.cc",
        "caffe2/operators/deform_conv_op.cc",
        "caffe2/operators/dense_vector_to_id_list_op.cc",
        "caffe2/operators/distance_op.cc",
        "caffe2/operators/do_op.cc",
        "caffe2/operators/dropout_op.cc",
        "caffe2/operators/elementwise_add_gradient_op.cc",
        "caffe2/operators/elementwise_add_op.cc",
        "caffe2/operators/elementwise_div_gradient_op.cc",
        "caffe2/operators/elementwise_div_op.cc",
        "caffe2/operators/elementwise_linear_op.cc",
        "caffe2/operators/elementwise_logical_ops.cc",
        "caffe2/operators/elementwise_mul_gradient_op.cc",
        "caffe2/operators/elementwise_mul_op.cc",
        "caffe2/operators/elementwise_ops.cc",
        "caffe2/operators/elementwise_ops_schema.cc",
        "caffe2/operators/elementwise_ops_utils.cc",
        "caffe2/operators/elementwise_sub_gradient_op.cc",
        "caffe2/operators/elementwise_sub_op.cc",
        "caffe2/operators/elementwise_sum_op.cc",
        "caffe2/operators/elu_op.cc",
        "caffe2/operators/enforce_finite_op.cc",
        "caffe2/operators/ensure_clipped_op.cc",
        "caffe2/operators/ensure_cpu_output_op.cc",
        "caffe2/operators/erf_op.cc",
        "caffe2/operators/exp_op.cc",
        "caffe2/operators/expand_op.cc",
        "caffe2/operators/expand_squeeze_dims_op.cc",
        "caffe2/operators/fc_inference.cc",
        "caffe2/operators/feature_maps_ops.cc",
        "caffe2/operators/feed_blob_op.cc",
        "caffe2/operators/filler_op.cc",
        "caffe2/operators/find_duplicate_elements_op.cc",
        "caffe2/operators/find_op.cc",
        "caffe2/operators/flatten_op.cc",
        "caffe2/operators/flexible_top_k.cc",
        "caffe2/operators/floor_op.cc",
        "caffe2/operators/free_op.cc",
        "caffe2/operators/fully_connected_op.cc",
        "caffe2/operators/fused_rowwise_8bit_conversion_ops.cc",
        "caffe2/operators/fused_rowwise_random_quantization_ops.cc",
        "caffe2/operators/gather_fused_8bit_rowwise_op.cc",
        "caffe2/operators/gather_op.cc",
        "caffe2/operators/gather_ranges_to_dense_op.cc",
        "caffe2/operators/gelu_op.cc",
        "caffe2/operators/generate_proposals_op.cc",
        "caffe2/operators/given_tensor_byte_string_to_uint8_fill_op.cc",
        "caffe2/operators/given_tensor_fill_op.cc",
        "caffe2/operators/glu_op.cc",
        "caffe2/operators/group_norm_op.cc",
        "caffe2/operators/gru_unit_op.cc",
        "caffe2/operators/h_softmax_op.cc",
        "caffe2/operators/half_float_ops.cc",
        "caffe2/operators/hard_sigmoid_op.cc",
        "caffe2/operators/heatmap_max_keypoint_op.cc",
        "caffe2/operators/if_op.cc",
        "caffe2/operators/im2col_op.cc",
        "caffe2/operators/index_hash_ops.cc",
        "caffe2/operators/index_ops.cc",
        "caffe2/operators/inference_lstm_op.cc",
        "caffe2/operators/instance_norm_gradient_op.cc",
        "caffe2/operators/instance_norm_op.cc",
        "caffe2/operators/integral_image_op.cc",
        "caffe2/operators/is_empty_op.cc",
        "caffe2/operators/jsd_op.cc",
        "caffe2/operators/key_split_ops.cc",
        "caffe2/operators/last_n_window_collector.cc",
        "caffe2/operators/layer_norm_op.cc",
        "caffe2/operators/leaky_relu_op.cc",
        "caffe2/operators/length_split_op.cc",
        "caffe2/operators/lengths_pad_op.cc",
        "caffe2/operators/lengths_reducer_fused_8bit_rowwise_ops.cc",
        "caffe2/operators/lengths_reducer_ops.cc",
        "caffe2/operators/lengths_reducer_rowwise_8bit_ops.cc",
        "caffe2/operators/lengths_tile_op.cc",
        "caffe2/operators/lengths_top_k_op.cc",
        "caffe2/operators/listwise_l2r_op.cc",
        "caffe2/operators/load_save_op.cc",
        "caffe2/operators/load_save_op_util.cc",
        "caffe2/operators/local_response_normalization_op.cc",
        "caffe2/operators/locally_connected_op.cc",
        "caffe2/operators/locally_connected_op_util.cc",
        "caffe2/operators/log_op.cc",
        "caffe2/operators/logit_op.cc",
        "caffe2/operators/loss_op.cc",
        "caffe2/operators/lp_pool_op.cc",
        "caffe2/operators/lpnorm_op.cc",
        "caffe2/operators/lstm_unit_op.cc",
        "caffe2/operators/map_ops.cc",
        "caffe2/operators/margin_ranking_criterion_op.cc",
        "caffe2/operators/matmul_op.cc",
        "caffe2/operators/mean_op.cc",
        "caffe2/operators/merge_id_lists_op.cc",
        "caffe2/operators/minmax_gradient_ops.cc",
        "caffe2/operators/minmax_ops.cc",
        "caffe2/operators/mod_op.cc",
        "caffe2/operators/moments_op.cc",
        "caffe2/operators/multi_class_accuracy_op.cc",
        "caffe2/operators/negate_gradient_op.cc",
        "caffe2/operators/negative_op.cc",
        "caffe2/operators/ngram_ops.cc",
        "caffe2/operators/norm_planar_yuv_op.cc",
        "caffe2/operators/normalize_l1_op.cc",
        "caffe2/operators/normalize_op.cc",
        "caffe2/operators/numpy_tile_op.cc",
        "caffe2/operators/one_hot_ops.cc",
        "caffe2/operators/onnx_while_op.cc",
        "caffe2/operators/order_switch_ops.cc",
        "caffe2/operators/pack_rnn_sequence_op.cc",
        "caffe2/operators/pack_segments.cc",
        "caffe2/operators/pad_op.cc",
        "caffe2/operators/partition_ops.cc",
        "caffe2/operators/percentile_op.cc",
        "caffe2/operators/perplexity_op.cc",
        "caffe2/operators/piecewise_linear_transform_op.cc",
        "caffe2/operators/pool_gradient_op.cc",
        "caffe2/operators/pool_op.cc",
        "caffe2/operators/pool_op_util.cc",
        "caffe2/operators/pow_op.cc",
        "caffe2/operators/prelu_op.cc",
        "caffe2/operators/prepend_dim_op.cc",
        "caffe2/operators/quant_decode_op.cc",
        "caffe2/operators/rank_loss_op.cc",
        "caffe2/operators/reciprocal_gradient_op.cc",
        "caffe2/operators/reciprocal_op.cc",
        "caffe2/operators/reduce_front_back_max_ops.cc",
        "caffe2/operators/reduce_front_back_mean_ops.cc",
        "caffe2/operators/reduce_front_back_sum_ops.cc",
        "caffe2/operators/reduce_ops.cc",
        "caffe2/operators/reduction_ops.cc",
        "caffe2/operators/relu_n_op.cc",
        "caffe2/operators/relu_op.cc",
        "caffe2/operators/remove_data_blocks_op.cc",
        "caffe2/operators/replace_nan_op.cc",
        "caffe2/operators/reservoir_sampling.cc",
        "caffe2/operators/reshape_op.cc",
        "caffe2/operators/resize_3d_op.cc",
        "caffe2/operators/resize_op.cc",
        "caffe2/operators/reverse_packed_segs_op.cc",
        "caffe2/operators/rmac_regions_op.cc",
        "caffe2/operators/rnn/recurrent_network_blob_fetcher_op.cc",
        "caffe2/operators/rnn/recurrent_network_executor.cc",
        "caffe2/operators/rnn/recurrent_network_op.cc",
        "caffe2/operators/roi_align_gradient_op.cc",
        "caffe2/operators/roi_align_op.cc",
        "caffe2/operators/roi_align_rotated_gradient_op.cc",
        "caffe2/operators/roi_align_rotated_op.cc",
        "caffe2/operators/roi_pool_op.cc",
        "caffe2/operators/rowmul_op.cc",
        "caffe2/operators/rsqrt_op.cc",
        "caffe2/operators/scale_blobs_op.cc",
        "caffe2/operators/scale_op.cc",
        "caffe2/operators/segment_reduction_op.cc",
        "caffe2/operators/selu_op.cc",
        "caffe2/operators/sequence_ops.cc",
        "caffe2/operators/shape_op.cc",
        "caffe2/operators/sigmoid_gradient_op.cc",
        "caffe2/operators/sigmoid_op.cc",
        "caffe2/operators/sin_op.cc",
        "caffe2/operators/sinh_op.cc",
        "caffe2/operators/sinusoid_position_encoding_op.cc",
        "caffe2/operators/slice_op.cc",
        "caffe2/operators/softmax_op.cc",
        "caffe2/operators/softmax_utils.cc",
        "caffe2/operators/softmax_with_loss_op.cc",
        "caffe2/operators/softplus_op.cc",
        "caffe2/operators/softsign_op.cc",
        "caffe2/operators/space_batch_op.cc",
        "caffe2/operators/sparse_dropout_with_replacement_op.cc",
        "caffe2/operators/sparse_normalize_op.cc",
        "caffe2/operators/sparse_to_dense_mask_op.cc",
        "caffe2/operators/sparse_to_dense_op.cc",
        "caffe2/operators/spatial_batch_norm_gradient_op.cc",
        "caffe2/operators/spatial_batch_norm_op.cc",
        "caffe2/operators/spatial_softmax_with_loss_op.cc",
        "caffe2/operators/sqr_op.cc",
        "caffe2/operators/sqrt_op.cc",
        "caffe2/operators/square_root_divide_op.cc",
        "caffe2/operators/stats_ops.cc",
        "caffe2/operators/stats_put_ops.cc",
        "caffe2/operators/stop_gradient.cc",
        "caffe2/operators/string_ops.cc",
        "caffe2/operators/stump_func_op.cc",
        "caffe2/operators/stylizer_ops.cc",
        "caffe2/operators/summarize_op.cc",
        "caffe2/operators/swish_op.cc",
        "caffe2/operators/tan_op.cc",
        "caffe2/operators/tanh_gradient_op.cc",
        "caffe2/operators/tanh_op.cc",
        "caffe2/operators/tensor_protos_db_input.cc",
        "caffe2/operators/text_file_reader.cc",
        "caffe2/operators/text_file_reader_utils.cc",
        "caffe2/operators/thresholded_relu_op.cc",
        "caffe2/operators/tile_op.cc",
        "caffe2/operators/top_k.cc",
        "caffe2/operators/transpose_op.cc",
        "caffe2/operators/tt_linear_op.cc",
        "caffe2/operators/unique_ops.cc",
        "caffe2/operators/upsample_op.cc",
        "caffe2/operators/utility_ops.cc",
        "caffe2/operators/variable_length_sequence_padding.cc",
        "caffe2/operators/weighted_multi_sampling_op.cc",
        "caffe2/operators/weighted_sample_op.cc",
        "caffe2/operators/while_op.cc",
        "caffe2/operators/workspace_ops.cc",
        "caffe2/operators/zero_gradient_op.cc",
    ],
)

filegroup(
    name = "caffe2_opt_srcs",
    srcs = [
        "caffe2/opt/annotations.cc",
        "caffe2/opt/backend_cutting.cc",
        "caffe2/opt/backend_transformer_base.cc",
        "caffe2/opt/bound_shape_inferencer.cc",
        "caffe2/opt/converter.cc",
        "caffe2/opt/dead_code_elim.cc",
        "caffe2/opt/device.cc",
        "caffe2/opt/distributed.cc",
        "caffe2/opt/distributed_converter.cc",
        "caffe2/opt/fusion.cc",
        "caffe2/opt/mobile.cc",
        "caffe2/opt/onnxifi_op.cc",
        "caffe2/opt/onnxifi_transformer.cc",
        "caffe2/opt/optimize_ideep.cc",
        "caffe2/opt/optimizer.cc",
        "caffe2/opt/passes.cc",
        "caffe2/opt/shape_info.cc",
        "caffe2/opt/tvm_transformer.cc",
    ],
)

filegroup(
    name = "caffe2_perfkernels_srcs",
    srcs = [
        "caffe2/perfkernels/adagrad.cc",
        "caffe2/perfkernels/embedding_lookup.cc",
        "caffe2/perfkernels/embedding_lookup_idx.cc",
        "caffe2/perfkernels/fused_8bit_rowwise_embedding_lookup.cc",
        "caffe2/perfkernels/fused_8bit_rowwise_embedding_lookup_idx.cc",
        "caffe2/perfkernels/fused_nbit_rowwise_conversion.cc",
        "caffe2/perfkernels/lstm_unit_cpu_common.cc",
        "caffe2/perfkernels/math_cpu_base.cc",
        "caffe2/perfkernels/typed_axpy.cc",
    ],
)

filegroup(
    name = "caffe2_predictor_srcs",
    srcs = [
        "caffe2/predictor/emulator/data_filler.cc",
        "caffe2/predictor/emulator/data_filler.h",
        "caffe2/predictor/predictor.cc",
        "caffe2/predictor/predictor_config.cc",
        "caffe2/predictor/predictor_utils.cc",
    ],
)

filegroup(
    name = "caffe2_quantization_srcs",
    srcs = [
        "caffe2/quantization/server/activation_distribution_observer.cc",
        "caffe2/quantization/server/batch_matmul_dnnlowp_op.cc",
        "caffe2/quantization/server/caffe2_dnnlowp_utils.cc",
        "caffe2/quantization/server/channel_shuffle_dnnlowp_op.cc",
        "caffe2/quantization/server/concat_dnnlowp_op.cc",
        "caffe2/quantization/server/conv_dnnlowp_acc16_op.cc",
        "caffe2/quantization/server/conv_dnnlowp_op.cc",
        "caffe2/quantization/server/conv_relu_op.cc",
        "caffe2/quantization/server/dequantize_dnnlowp_op.cc",
        "caffe2/quantization/server/dnnlowp.cc",
        "caffe2/quantization/server/dnnlowp_partition.cc",
        "caffe2/quantization/server/dynamic_histogram.cc",
        "caffe2/quantization/server/elementwise_add_dnnlowp_op.cc",
        "caffe2/quantization/server/elementwise_linear_dnnlowp_op.cc",
        "caffe2/quantization/server/elementwise_mul_dnnlowp_op.cc",
        "caffe2/quantization/server/elementwise_sum_dnnlowp_op.cc",
        "caffe2/quantization/server/elementwise_sum_relu_op.cc",
        "caffe2/quantization/server/fbgemm_pack_matrix_cache.cc",
        "caffe2/quantization/server/fbgemm_pack_op.cc",
        "caffe2/quantization/server/fully_connected_dnnlowp_acc16_op.cc",
        "caffe2/quantization/server/fully_connected_dnnlowp_op.cc",
        "caffe2/quantization/server/fully_connected_fake_lowp_op.cc",
        "caffe2/quantization/server/group_norm_dnnlowp_op.cc",
        "caffe2/quantization/server/int8_gen_quant_params.cc",
        "caffe2/quantization/server/kl_minimization.cc",
        "caffe2/quantization/server/lstm_unit_dnnlowp_op.cc",
        "caffe2/quantization/server/norm_minimization.cc",
        "caffe2/quantization/server/p99.cc",
        "caffe2/quantization/server/pool_dnnlowp_op.cc",
        "caffe2/quantization/server/quantize_dnnlowp_op.cc",
        "caffe2/quantization/server/relu_dnnlowp_op.cc",
        "caffe2/quantization/server/sigmoid.cc",
        "caffe2/quantization/server/sigmoid_dnnlowp_op.cc",
        "caffe2/quantization/server/spatial_batch_norm_dnnlowp_op.cc",
        "caffe2/quantization/server/tanh.cc",
        "caffe2/quantization/server/tanh_dnnlowp_op.cc",
        "caffe2/quantization/server/utility_dnnlowp_ops.cc",
    ],
)

filegroup(
    name = "caffe2_queue_srcs",
    srcs = [
        "caffe2/queue/blobs_queue.cc",
        "caffe2/queue/blobs_queue_db.cc",
        "caffe2/queue/queue_ops.cc",
        "caffe2/queue/rebatching_queue.cc",
        "caffe2/queue/rebatching_queue_ops.cc",
    ],
)

filegroup(
    name = "caffe2_serialize_srcs",
    srcs = [
        "caffe2/serialize/file_adapter.cc",
        "caffe2/serialize/inline_container.cc",
        "caffe2/serialize/istream_adapter.cc",
        "caffe2/serialize/read_adapter_interface.cc",
    ],
)

filegroup(
    name = "caffe2_sgd_srcs",
    srcs = [
        "caffe2/sgd/adadelta_op.cc",
        "caffe2/sgd/adagrad_op.cc",
        "caffe2/sgd/adam_op.cc",
        "caffe2/sgd/clip_tensor_op.cc",
        "caffe2/sgd/ftrl_op.cc",
        "caffe2/sgd/gftrl_op.cc",
        "caffe2/sgd/iter_op.cc",
        "caffe2/sgd/lars_op.cc",
        "caffe2/sgd/learning_rate_adaption_op.cc",
        "caffe2/sgd/learning_rate_op.cc",
        "caffe2/sgd/momentum_sgd_op.cc",
        "caffe2/sgd/rmsprop_op.cc",
        "caffe2/sgd/wngrad_op.cc",
        "caffe2/sgd/yellowfin_op.cc",
    ],
)

filegroup(
    name = "caffe2_transforms_srcs",
    srcs = [
        "caffe2/transforms/common_subexpression_elimination.cc",
        "caffe2/transforms/conv_to_nnpack_transform.cc",
        "caffe2/transforms/pattern_net_transform.cc",
        "caffe2/transforms/single_op_transform.cc",
    ],
)

filegroup(
    name = "caffe2_utils_srcs",
    srcs = [
        "caffe2/utils/bench_utils.cc",
        "caffe2/utils/cpuid.cc",
        "caffe2/utils/math/broadcast.cc",
        "caffe2/utils/math/elementwise.cc",
        "caffe2/utils/math/reduce.cc",
        "caffe2/utils/math/transpose.cc",
        "caffe2/utils/math/utils.cc",
        "caffe2/utils/math_cpu.cc",
        "caffe2/utils/murmur_hash3.cc",
        "caffe2/utils/proto_convert.cc",
        "caffe2/utils/proto_utils.cc",
        "caffe2/utils/proto_wrap.cc",
        "caffe2/utils/signal_handler.cc",
        "caffe2/utils/smart_tensor_printer.cc",
        "caffe2/utils/string_utils.cc",
        "caffe2/utils/threadpool/ThreadPool.cc",
        "caffe2/utils/threadpool/pthreadpool.cc",
        "caffe2/utils/threadpool/pthreadpool_impl.cc",
        "caffe2/utils/threadpool/thread_pool_guard.cpp",
    ],
)

filegroup(
    name = "caffe2_cuda_cpp_srcs",
    srcs = [
        "caffe2/contrib/aten/aten_op_gpu.cc",
        "caffe2/contrib/gloo/allreduce_ops_gpu.cc",
        "caffe2/contrib/gloo/broadcast_ops_gpu.cc",
        "caffe2/contrib/gloo/common_world_ops_gpu.cc",
        "caffe2/core/blob_serialization_gpu.cc",
        "caffe2/core/common_cudnn.cc",
        "caffe2/core/common_gpu.cc",
        "caffe2/core/event_gpu.cc",
        "caffe2/db/create_db_op_gpu.cc",
        "caffe2/distributed/file_store_handler_op_gpu.cc",
        "caffe2/operators/communicator_op_gpu.cc",
        "caffe2/operators/concat_split_op_gpu.cc",
        "caffe2/operators/conv_op_cache_cudnn.cc",
        "caffe2/operators/conv_op_cudnn.cc",
        "caffe2/operators/conv_op_gpu.cc",
        "caffe2/operators/conv_op_shared_gpu.cc",
        "caffe2/operators/conv_transpose_op_cudnn.cc",
        "caffe2/operators/conv_transpose_op_gpu.cc",
        "caffe2/operators/counter_ops_gpu.cc",
        "caffe2/operators/do_op_gpu.cc",
        "caffe2/operators/dropout_op_cudnn.cc",
        "caffe2/operators/elementwise_add_op_gpu.cc",
        "caffe2/operators/elementwise_sub_op_gpu.cc",
        "caffe2/operators/elu_op_cudnn.cc",
        "caffe2/operators/exp_op_gpu.cc",
        "caffe2/operators/expand_op_gpu.cc",
        "caffe2/operators/expand_squeeze_dims_op_gpu.cc",
        "caffe2/operators/free_op_gpu.cc",
        "caffe2/operators/fully_connected_op_gpu.cc",
        "caffe2/operators/if_op_gpu.cc",
        "caffe2/operators/im2col_op_gpu.cc",
        "caffe2/operators/load_save_op_gpu.cc",
        "caffe2/operators/local_response_normalization_op_cudnn.cc",
        "caffe2/operators/locally_connected_op_gpu.cc",
        "caffe2/operators/log_op_gpu.cc",
        "caffe2/operators/matmul_op_gpu.cc",
        "caffe2/operators/negate_gradient_op_gpu.cc",
        "caffe2/operators/negative_op_gpu.cc",
        "caffe2/operators/order_switch_ops_cudnn.cc",
        "caffe2/operators/order_switch_ops_gpu.cc",
        "caffe2/operators/pool_op_cudnn.cc",
        "caffe2/operators/prepend_dim_op_gpu.cc",
        "caffe2/operators/reshape_op_gpu.cc",
        "caffe2/operators/rnn/recurrent_network_blob_fetcher_op_gpu.cc",
        "caffe2/operators/rnn/recurrent_network_executor_gpu.cc",
        "caffe2/operators/rnn/recurrent_op_cudnn.cc",
        "caffe2/operators/scale_op_gpu.cc",
        "caffe2/operators/shape_op_gpu.cc",
        "caffe2/operators/sigmoid_op_cudnn.cc",
        "caffe2/operators/softmax_op_cudnn.cc",
        "caffe2/operators/sqr_op_gpu.cc",
        "caffe2/operators/sqrt_op_gpu.cc",
        "caffe2/operators/stop_gradient_gpu.cc",
        "caffe2/operators/tanh_op_cudnn.cc",
        "caffe2/operators/tensor_protos_db_input_gpu.cc",
        "caffe2/operators/transpose_op_cudnn.cc",
        "caffe2/operators/while_op_gpu.cc",
        "caffe2/operators/zero_gradient_op_gpu.cc",
        "caffe2/queue/queue_ops_gpu.cc",
        "caffe2/sgd/iter_op_gpu.cc",
        "caffe2/sgd/learning_rate_op_gpu.cc",
    ],
)

filegroup(
    name = "caffe2_cu_srcs",
    srcs = [
        "caffe2/core/context_gpu.cu",
        "caffe2/operators/abs_op.cu",
        "caffe2/operators/accumulate_op.cu",
        "caffe2/operators/accuracy_op.cu",
        "caffe2/operators/acos_op.cu",
        "caffe2/operators/affine_channel_op.cu",
        "caffe2/operators/alias_with_name.cu",
        "caffe2/operators/arg_ops.cu",
        "caffe2/operators/asin_op.cu",
        "caffe2/operators/assert_op.cu",
        "caffe2/operators/atan_op.cu",
        "caffe2/operators/batch_gather_ops.cu",
        "caffe2/operators/batch_matmul_op.cu",
        "caffe2/operators/batch_moments_op.cu",
        "caffe2/operators/batch_permutation_op.cu",
        "caffe2/operators/batch_sparse_to_dense_op.cu",
        "caffe2/operators/boolean_mask_ops.cu",
        "caffe2/operators/boolean_unmask_ops.cu",
        "caffe2/operators/bucketize_op.cu",
        "caffe2/operators/cast_op.cu",
        "caffe2/operators/cbrt_op.cu",
        "caffe2/operators/ceil_op.cu",
        "caffe2/operators/channel_backprop_stats_op.cu",
        "caffe2/operators/channel_shuffle_op.cu",
        "caffe2/operators/channel_stats_op.cu",
        "caffe2/operators/channelwise_conv3d_op_cudnn.cu",
        "caffe2/operators/clip_op.cu",
        "caffe2/operators/copy_op.cu",
        "caffe2/operators/cos_op.cu",
        "caffe2/operators/cosh_op.cu",
        "caffe2/operators/cosine_embedding_criterion_op.cu",
        "caffe2/operators/cross_entropy_op.cu",
        "caffe2/operators/cube_op.cu",
        "caffe2/operators/data_couple_gpu.cu",
        "caffe2/operators/deform_conv_op.cu",
        "caffe2/operators/depthwise_3x3_conv_op_cudnn.cu",
        "caffe2/operators/distance_op.cu",
        "caffe2/operators/dropout_op.cu",
        "caffe2/operators/elementwise_div_op.cu",
        "caffe2/operators/elementwise_linear_op.cu",
        "caffe2/operators/elementwise_mul_op.cu",
        "caffe2/operators/elementwise_ops.cu",
        "caffe2/operators/elu_op.cu",
        "caffe2/operators/enforce_finite_op.cu",
        "caffe2/operators/ensure_cpu_output_op.cu",
        "caffe2/operators/erf_op.cu",
        "caffe2/operators/filler_op.cu",
        "caffe2/operators/find_op.cu",
        "caffe2/operators/floor_op.cu",
        "caffe2/operators/gather_op.cu",
        "caffe2/operators/gelu_op.cu",
        "caffe2/operators/generate_proposals_op.cu",
        "caffe2/operators/generate_proposals_op_util_nms_gpu.cu",
        "caffe2/operators/given_tensor_byte_string_to_uint8_fill_op.cu",
        "caffe2/operators/given_tensor_fill_op.cu",
        "caffe2/operators/glu_op.cu",
        "caffe2/operators/group_norm_op.cu",
        "caffe2/operators/gru_unit_op_gpu.cu",
        "caffe2/operators/half_float_ops.cu",
        "caffe2/operators/hard_sigmoid_op.cu",
        "caffe2/operators/instance_norm_op.cu",
        "caffe2/operators/integral_image_op.cu",
        "caffe2/operators/layer_norm_op.cu",
        "caffe2/operators/leaky_relu_op.cu",
        "caffe2/operators/lengths_pad_op.cu",
        "caffe2/operators/lengths_tile_op.cu",
        "caffe2/operators/local_response_normalization_op.cu",
        "caffe2/operators/logit_op.cu",
        "caffe2/operators/loss_op.cu",
        "caffe2/operators/lp_pool_op.cu",
        "caffe2/operators/lstm_unit_op_gpu.cu",
        "caffe2/operators/margin_ranking_criterion_op.cu",
        "caffe2/operators/max_pool_with_index.cu",
        "caffe2/operators/mean_op.cu",
        "caffe2/operators/mem_query_op.cu",
        "caffe2/operators/minmax_ops.cu",
        "caffe2/operators/moments_op.cu",
        "caffe2/operators/multi_class_accuracy_op.cu",
        "caffe2/operators/normalize_ops.cu",
        "caffe2/operators/one_hot_ops.cu",
        "caffe2/operators/pack_segments.cu",
        "caffe2/operators/pad_op_gpu.cu",
        "caffe2/operators/perplexity_op.cu",
        "caffe2/operators/piecewise_linear_transform_op.cu",
        "caffe2/operators/pool_op.cu",
        "caffe2/operators/pow_op.cu",
        "caffe2/operators/prelu_op.cu",
        "caffe2/operators/reciprocal_op.cu",
        "caffe2/operators/reduce_front_back_max_ops.cu",
        "caffe2/operators/reduce_front_back_sum_mean_ops.cu",
        "caffe2/operators/reduce_ops.cu",
        "caffe2/operators/reduction_ops.cu",
        "caffe2/operators/relu_n_op.cu",
        "caffe2/operators/relu_op.cu",
        "caffe2/operators/replace_nan_op.cu",
        "caffe2/operators/resize_3d_op.cu",
        "caffe2/operators/resize_op.cu",
        "caffe2/operators/reverse_packed_segs_op.cu",
        "caffe2/operators/rmac_regions_op.cu",
        "caffe2/operators/rnn/recurrent_network_op_gpu.cu",
        "caffe2/operators/roi_align_gradient_op.cu",
        "caffe2/operators/roi_align_op.cu",
        "caffe2/operators/roi_align_rotated_gradient_op.cu",
        "caffe2/operators/roi_align_rotated_op.cu",
        "caffe2/operators/roi_pool_op.cu",
        "caffe2/operators/rsqrt_op.cu",
        "caffe2/operators/scale_blobs_op.cu",
        "caffe2/operators/segment_reduction_op_gpu.cu",
        "caffe2/operators/selu_op.cu",
        "caffe2/operators/sequence_ops.cu",
        "caffe2/operators/sigmoid_op.cu",
        "caffe2/operators/sin_op.cu",
        "caffe2/operators/sinh_op.cu",
        "caffe2/operators/slice_op.cu",
        "caffe2/operators/softmax_ops.cu",
        "caffe2/operators/softplus_op.cu",
        "caffe2/operators/softsign_op.cu",
        "caffe2/operators/space_batch_op_gpu.cu",
        "caffe2/operators/sparse_normalize_op_gpu.cu",
        "caffe2/operators/sparse_to_dense_op.cu",
        "caffe2/operators/spatial_batch_norm_op.cu",
        "caffe2/operators/spatial_batch_norm_op_cudnn.cu",
        "caffe2/operators/stump_func_op.cu",
        "caffe2/operators/summarize_op.cu",
        "caffe2/operators/swish_op.cu",
        "caffe2/operators/tan_op.cu",
        "caffe2/operators/tanh_op.cu",
        "caffe2/operators/thresholded_relu_op.cu",
        "caffe2/operators/tile_op.cu",
        "caffe2/operators/top_k.cu",
        "caffe2/operators/transpose_op.cu",
        "caffe2/operators/unique_ops.cu",
        "caffe2/operators/upsample_op.cu",
        "caffe2/operators/utility_ops.cu",
        "caffe2/operators/weighted_sample_op.cu",
        "caffe2/sgd/adadelta_op_gpu.cu",
        "caffe2/sgd/adagrad_op_gpu.cu",
        "caffe2/sgd/adam_op_gpu.cu",
        "caffe2/sgd/fp16_momentum_sgd_op.cu",
        "caffe2/sgd/fp32_momentum_sgd_op.cu",
        "caffe2/sgd/lars_op_gpu.cu",
        "caffe2/sgd/momentum_sgd_op_gpu.cu",
        "caffe2/sgd/rmsprop_op_gpu.cu",
        "caffe2/sgd/yellowfin_op_gpu.cu",
        "caffe2/utils/math/broadcast.cu",
        "caffe2/utils/math/elementwise.cu",
        "caffe2/utils/math/reduce.cu",
        "caffe2/utils/math/transpose.cu",
        "caffe2/utils/math_gpu.cu",
    ],
)

# To achieve finer granularity and make debug easier, caffe2 is split into three libraries:
# ATen, caffe2 and caffe2_for_aten_headers. ATen lib group up source codes under
# aten/ directory and caffe2 contains most files under `caffe2/` directory. Since the
# ATen lib and the caffe2 lib would depend on each other, `caffe2_for_aten_headers` is splitted
# out from `caffe2` to avoid dependency cycle.
cc_library(
    name = "caffe2_for_aten_headers",
    hdrs = [
        "caffe2/core/common.h",
        "caffe2/core/logging.h",
        "caffe2/core/types.h",
        "caffe2/perfkernels/common.h",
        "caffe2/perfkernels/embedding_lookup.h",
        "caffe2/perfkernels/embedding_lookup_idx.h",
        "caffe2/utils/fixed_divisor.h",
        "caffe2/utils/cpuid.h",
    ] + glob([
        "caffe2/utils/threadpool/*.h",
        "caffe2/proto/*.h",
    ]),
    copts = CAFFE2_COPTS,
    visibility = ["//visibility:public"],
    deps = [
        ":caffe2_protos",
        ":caffe2_core_macros_h",
        "//c10:headers",
    ],
)

py_binary(
    name = "gen_op",
    srcs = ["caffe2/contrib/aten/gen_op.py"],
    deps = ["//torchgen"],
)

genrule(
    name = "generated_caffe2_aten_op_headers",
    srcs = [
        "caffe2/contrib/aten/aten_op_template.h",
        "aten/src/ATen/Declarations.yaml",
    ],
    outs = ["caffe2/caffe2/contrib/aten/gen_aten_op.h"],
    cmd = """
    $(location :gen_op) \
        --output_prefix gen_ \
        --install_dir $(@D) \
        --aten_root `dirname $(location aten/src/ATen/Declarations.yaml)`/../.. \
        --template_dir `dirname $(location caffe2/contrib/aten/aten_op_template.h)` \
        --yaml_dir `dirname $(location aten/src/ATen/Declarations.yaml)`""",
    tools = [":gen_op"],
)

cc_library(
    name = "caffe2_headers",
    hdrs = glob([
        "caffe2/contrib/aten/*.h",
        "caffe2/contrib/gloo/*.h",
        "caffe2/core/*.h",
        "caffe2/core/nomnigraph/include/nomnigraph/Converters/*.h",
        "caffe2/core/nomnigraph/include/nomnigraph/Generated/*.h",
        "caffe2/core/nomnigraph/include/nomnigraph/Graph/*.h",
        "caffe2/core/nomnigraph/include/nomnigraph/Representations/*.h",
        "caffe2/core/nomnigraph/include/nomnigraph/Support/*.h",
        "caffe2/core/nomnigraph/include/nomnigraph/Transformations/*.h",
        "caffe2/core/nomnigraph/tests/*.h",
        "caffe2/db/*.h",
        "caffe2/distributed/*.h",
        "caffe2/ideep/*.h",
        "caffe2/ideep/operators/*.h",
        "caffe2/ideep/operators/quantization/*.h",
        "caffe2/ideep/utils/*.h",
        "caffe2/onnx/*.h",
        "caffe2/operators/*.h",
        "caffe2/operators/rnn/*.h",
        "caffe2/opt/*.h",
        "caffe2/perfkernels/*.h",
        "caffe2/predictor/*.h",
        "caffe2/predictor/emulator/*.h",
        "caffe2/proto/*.h",
        "caffe2/quantization/server/*.h",
        "caffe2/queue/*.h",
        "caffe2/serialize/*.h",
        "caffe2/sgd/*.h",
        "caffe2/share/contrib/depthwise/*.h",
        "caffe2/transforms/*.h",
        "caffe2/utils/*.h",
        "caffe2/utils/math/*.h",
        "caffe2/utils/threadpool/*.h",
        "modules/**/*.h",
    ]) + if_cuda(glob([
        "caffe2/**/*.cuh",
        "caffe2/image/*.h",
    ])) + [":generated_caffe2_aten_op_headers"],
    copts = CAFFE2_COPTS,
    includes = [
        "caffe2/contrib/aten",
        "caffe2/core/nomnigraph/include",
        "third_party/miniz-2.1.0",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":caffe2_for_aten_headers",
        ":caffe2_protos",
    ],
)

cc_library(
    name = "caffe2_dnnlowp_avx2_ops",
    srcs = [
        "caffe2/quantization/server/elementwise_sum_dnnlowp_op_avx2.cc",
        "caffe2/quantization/server/fully_connected_fake_lowp_op_avx2.cc",
        "caffe2/quantization/server/group_norm_dnnlowp_op_avx2.cc",
        "caffe2/quantization/server/norm_minimization_avx2.cc",
        "caffe2/quantization/server/pool_dnnlowp_op_avx2.cc",
        "caffe2/quantization/server/relu_dnnlowp_op_avx2.cc",
        "caffe2/quantization/server/spatial_batch_norm_dnnlowp_op_avx2.cc",
        "caffe2/quantization/server/transpose.cc",
    ],
    copts = CAFFE2_COPTS + [
        "-mf16c",
        "-mavx2",
        "-mfma",
        "-mxsave",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":caffe2_headers",
        "@fbgemm",
    ],
    alwayslink = True,
)

cc_library(
    name = "caffe2",
    srcs = [
        "caffe2/db/create_db_op.cc",
        "caffe2/db/protodb.cc",
        "caffe2/share/contrib/depthwise/depthwise3x3_conv_op.cc",
        ":caffe2_contrib_srcs",
        ":caffe2_core_srcs",
        ":caffe2_distributed_srcs",
        ":caffe2_ideep_srcs",
        ":caffe2_onnx_srcs",
        ":caffe2_operators_srcs",
        ":caffe2_opt_srcs",
        ":caffe2_perfkernels_srcs",
        ":caffe2_predictor_srcs",
        ":caffe2_quantization_srcs",
        ":caffe2_queue_srcs",
        ":caffe2_serialize_srcs",
        ":caffe2_sgd_srcs",
        ":caffe2_transforms_srcs",
        ":caffe2_utils_srcs",
    ],
    copts = CAFFE2_COPTS + ["-mf16c"],
    linkstatic = 1,
    visibility = ["//visibility:public"],
    deps = [
        ":caffe2_headers",
        ":caffe2_dnnlowp_avx2_ops",
        ":caffe2_perfkernels_avx",
        ":caffe2_perfkernels_avx2",
        ":caffe2_perfkernels_avx512",
        ":caffe2_protos",
        "//third_party/miniz-2.1.0:miniz",
        "@com_google_protobuf//:protobuf",
        "@eigen",
        "@fbgemm//:fbgemm_src_headers",
        "@foxi",
        "@gloo",
        "@onnx",
        "@fmt",
    ] + if_cuda(
        [
            ":caffe2_cuda_cpp",
            ":aten_cuda",
            "@tensorpipe//:tensorpipe_cuda",
        ],
        [
            ":aten",
            "@tensorpipe//:tensorpipe_cpu",
        ],
    ),
    alwayslink = True,
)

cc_library(
    name = "caffe2_cuda_cpp",
    srcs = [":caffe2_cuda_cpp_srcs"],
    copts = CAFFE2_COPTS,
    visibility = ["//visibility:public"],
    deps = [
        ":caffe2_cuda",
        ":caffe2_headers",
    ],
    alwayslink = True,
)

cu_library(
    name = "caffe2_cuda",
    srcs = [":caffe2_cu_srcs"],
    copts = CAFFE2_COPTS + torch_cuda_half_options,
    visibility = ["//visibility:public"],
    deps = [
        ":aten",
        ":caffe2_headers",
        "@cuda//:cublas",
        "@cuda//:curand",
        "@cudnn",
        "@eigen",
        "@gloo",
        "@tensorpipe//:tensorpipe_cuda",
    ],
    alwayslink = True,
)

PERF_COPTS = [
    "-DTH_HAVE_THREAD",
    "-DHAVE_AVX_CPU_DEFINITION",
    "-DHAVE_AVX2_CPU_DEFINITION",
    "-DENABLE_ALIAS=1",
    "-DHAVE_MALLOC_USABLE_SIZE=1",
    "-DHAVE_MMAP=1",
    "-DHAVE_SHM_OPEN=1",
    "-DHAVE_SHM_UNLINK=1",
    "-DSLEEF_STATIC_LIBS=1",
    "-DTH_BALS_MKL",
    "-D_FILE_OFFSET_BITS=64",
    "-DUSE_FBGEMM",
    "-fvisibility-inlines-hidden",
    "-Wunused-parameter",
    "-fno-math-errno",
    "-fno-trapping-math",
    "-mf16c",
]

PERF_HEADERS = glob([
    "caffe2/perfkernels/*.h",
    "caffe2/core/*.h",
])

cc_library(
    name = "caffe2_perfkernels_avx",
    srcs = glob([
        "caffe2/perfkernels/*_avx.cc",
    ]),
    hdrs = PERF_HEADERS,
    copts = PERF_COPTS + [
        "-mavx",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":caffe2_headers",
        "//c10",
    ],
    alwayslink = True,
)

cc_library(
    name = "caffe2_perfkernels_avx2",
    srcs = glob([
        "caffe2/perfkernels/*_avx2.cc",
    ]),
    hdrs = PERF_HEADERS,
    copts = PERF_COPTS + [
        "-mavx2",
        "-mfma",
        "-mavx",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":caffe2_headers",
        "//c10",
    ],
    alwayslink = True,
)

cc_library(
    name = "caffe2_perfkernels_avx512",
    srcs = [
        "caffe2/perfkernels/common_avx512.cc",
    ],
    hdrs = PERF_HEADERS,
    copts = PERF_COPTS + [
        "-mavx512f",
        "-mavx512dq",
        "-mavx512vl",
        "-mavx2",
        "-mfma",
        "-mavx",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":caffe2_headers",
        "//c10",
    ],
    alwayslink = True,
)

# torch
torch_cuda_headers = glob(["torch/csrc/cuda/*.h"])

cc_library(
    name = "torch_headers",
    hdrs = if_cuda(
        torch_cuda_headers,
    ) + glob(
        [
            "torch/*.h",
            "torch/csrc/**/*.h",
            "torch/csrc/distributed/c10d/*.hpp",
            "torch/lib/libshm/*.h",
        ],
        exclude = [
            "torch/csrc/autograd/generated/VariableType.h",
            "torch/csrc/autograd/generated/RegistrationDeclarations.h",
            "torch/csrc/autograd/generated/variable_factories.h",
            "torch/csrc/autograd/generated/Functions.h",
        ] + torch_cuda_headers,
    ) + GENERATED_AUTOGRAD_CPP + [":version_h"],
    includes = [
        "torch/csrc",
        "torch/csrc/api/include",
        "torch/csrc/distributed",
        "torch/lib",
        "torch/lib/libshm",
        "third_party/kineto/libkineto/include",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":aten_headers",
        ":caffe2_headers",
        "//c10:headers",
        "@com_github_google_flatbuffers//:flatbuffers",
        "@local_config_python//:python_headers",
        "@onnx",
    ],
    alwayslink = True,
)

TORCH_COPTS = COMMON_COPTS + [
    "-Dtorch_EXPORTS",
    "-DHAVE_AVX_CPU_DEFINITION",
    "-DHAVE_AVX2_CPU_DEFINITION",
    "-DCAFFE2_USE_GLOO",
    "-fvisibility-inlines-hidden",
    "-fno-math-errno ",
    "-fno-trapping-math",
]

cu_library(
    name = "torch_distributed_cuda",
    srcs = ["torch/csrc/distributed/c10d/quantization/quantization_gpu.cu"],
    deps = [":torch_headers"],
)

torch_sources = ({
    k: "" for k in (
    libtorch_core_sources +
    libtorch_distributed_sources +
    torch_cpp_srcs +
    libtorch_extra_sources +
    jit_core_sources +
    lazy_tensor_ts_sources +
    GENERATED_AUTOGRAD_CPP)
}).keys()

cc_library(
    name = "torch",
    srcs = if_cuda(glob(
        libtorch_cuda_sources,
        exclude = [
            "torch/csrc/cuda/python_nccl.cpp",
            "torch/csrc/cuda/nccl.cpp",
            "torch/csrc/distributed/c10d/quantization/quantization_gpu.cu",
        ],
    )) + torch_sources,
    copts = TORCH_COPTS,
    defines = [
        "CAFFE2_NIGHTLY_VERSION=20200115",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":caffe2",
        ":torch_headers",
        "@kineto",
    ] + if_cuda([
        ":torch_distributed_cuda",
        "@cuda//:nvToolsExt",
        "@cutlass",
    ]),
    alwayslink = True,
)

cc_library(
    name = "shm",
    srcs = glob(["torch/lib/libshm/*.cpp"]),
    deps = [
        ":torch",
    ],
)

cc_library(
    name = "libtorch_headers",
    hdrs = glob([
        "**/*.h",
        "**/*.cuh",
    ]) + [
        # We need the filegroup here because the raw list causes Bazel
        # to see duplicate files. It knows how to deduplicate with the
        # filegroup.
        ":cpp_generated_code"
    ],
    includes = [
        "torch/csrc/api/include",
        "torch/csrc/distributed",
        "torch/lib",
        "torch/lib/libshm",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":torch_headers",
    ],
)

cc_library(
    name = "torch_python",
    srcs = libtorch_python_core_sources + GENERATED_AUTOGRAD_PYTHON,
    deps = [
        ":torch",
        ":shm",
        "@pybind11",
    ],
)

pybind_extension(
    name = "_C",
    srcs = ["torch/csrc/stub.c"],
    deps = [
        ":torch_python"
    ],
)

# cpp api tests
cc_library(
    name = "test_support",
    testonly = True,
    srcs = [
        "test/cpp/api/support.cpp",
    ],
    hdrs = [
        "test/cpp/api/init_baseline.h",
        "test/cpp/api/optim_baseline.h",
        "test/cpp/api/support.h",
        "test/cpp/common/support.h",
    ],
    deps = [
        ":torch",
        "@com_google_googletest//:gtest_main",
    ],
)

# Torch integration tests rely on a labeled data set from the MNIST database.
# http://yann.lecun.com/exdb/mnist/

cpp_api_tests = glob(
    ["test/cpp/api/*.cpp"],
    exclude = [
        "test/cpp/api/imethod.cpp",
        "test/cpp/api/integration.cpp",
    ],
)

cc_test(
      name = "integration_test",
      size = "medium",
      srcs = ["test/cpp/api/integration.cpp"],
      deps = [
          ":test_support",
          "@com_google_googletest//:gtest_main",
      ],
      tags = [
        "gpu-required",
      ],
      data = [
        ":download_mnist",
      ],
)

[
  cc_test(
      name = paths.split_extension(paths.basename(filename))[0].replace("-","_") + "_test",
      size = "medium",
      srcs = [filename],
      deps = [
          ":test_support",
          "@com_google_googletest//:gtest_main",
      ],
  ) for filename in cpp_api_tests
]

test_suite(
    name = "api_tests",
    tests = [
        "any_test",
        "autograd_test",
        "dataloader_test",
        "enum_test",
        "expanding_array_test",
        "functional_test",
        "init_test",
        "integration_test",
        "jit_test",
        "memory_test",
        "misc_test",
        "module_test",
        "modulelist_test",
        "modules_test",
        "nn_utils_test",
        "optim_test",
        "ordered_dict_test",
        "rnn_test",
        "sequential_test",
        "serialize_test",
        "static_test",
        "tensor_options_test",
        "tensor_test",
        "torch_include_test",
    ],
)

# dist autograd tests
cc_test(
    name = "torch_dist_autograd_test",
    size = "small",
    srcs = ["test/cpp/dist_autograd/test_dist_autograd.cpp"],
    tags = [
        "exclusive",
        "gpu-required",
    ],
    deps = [
        ":torch",
        "@com_google_googletest//:gtest_main",
    ],
)

# jit tests
# Because these individual unit tests require custom registering,
# it is easier to mimic the cmake build by globing together a single test.
cc_test(
    name = "jit_tests",
    size = "small",
    srcs = glob([
        "test/cpp/jit/*.cpp",
        "test/cpp/jit/*.h",
        "test/cpp/tensorexpr/*.cpp",
        "test/cpp/tensorexpr/*.h",
    ], exclude=[
        # skip this since <pybind11/embed.h> is not found in OSS build
        "test/cpp/jit/test_exception.cpp",
    ]),
    linkstatic = True,
    tags = [
        "exclusive",
        "gpu-required",
    ],
    deps = [
        ":torch",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "lazy_tests",
    size = "small",
    srcs = glob([
        "test/cpp/lazy/*.cpp",
        "test/cpp/lazy/*.h",
    ], exclude=[
        # skip these since they depend on generated LazyIr.h which isn't available in bazel yet
        "test/cpp/lazy/test_ir.cpp",
        "test/cpp/lazy/test_lazy_ops.cpp",
        "test/cpp/lazy/test_lazy_ops_util.cpp",
    ]),
    linkstatic = True,
    tags = [
        "exclusive",
    ],
    deps = [
        ":torch",
        "@com_google_googletest//:gtest_main",
    ],
)

# all tests
test_suite(
    name = "all_tests",
    tests = [
        "api_tests",
        "jit_tests",
        "torch_dist_autograd_test",
        "//c10/test:tests",
    ],
)

# An internal genrule that we are converging with refers to these file
# as if they are from this package, so we alias them for
# compatibility.

[
    alias(
        name = paths.basename(path),
        actual = path,
    )
    for path in [
        "aten/src/ATen/templates/DispatchKeyNativeFunctions.cpp",
        "aten/src/ATen/templates/DispatchKeyNativeFunctions.h",
        "aten/src/ATen/templates/LazyIr.h",
        "aten/src/ATen/templates/LazyNonNativeIr.h",
        "aten/src/ATen/templates/RegisterDispatchKey.cpp",
        "aten/src/ATen/templates/RegisterDispatchDefinitions.ini",
        "aten/src/ATen/native/native_functions.yaml",
        "aten/src/ATen/native/tags.yaml",
        "aten/src/ATen/native/ts_native_functions.yaml",
        "torch/csrc/lazy/core/shape_inference.h",
        "torch/csrc/lazy/ts_backend/ts_native_functions.cpp",
    ]
]

genrule(
    name = "download_mnist",
    srcs = ["//:tools/download_mnist.py"],
    outs = [
        "mnist/train-images-idx3-ubyte",
        "mnist/train-labels-idx1-ubyte",
        "mnist/t10k-images-idx3-ubyte",
        "mnist/t10k-labels-idx1-ubyte",
    ],
    cmd = "python3 tools/download_mnist.py -d $(RULEDIR)/mnist",
)
