#define VS_FF_DEBUG 0x1L
#define VS_VERSION_INFO 0x1L
#define VS_FFI_FILEFLAGSMASK 0x17L
#define VER_PRIVATEBUILD 0x0L
#define VER_PRERELEASE 0x0L
#define VOS__WINDOWS32 0x4L
#define VFT_DLL 0x2L
#define VFT2_UNKNOWN 0x0L

#ifndef DEBUG
#define VER_DEBUG 0
#else
#define VER_DEBUG VS_FF_DEBUG
#endif


VS_VERSION_INFO VERSIONINFO
  FILEVERSION    @protobuf_RC_FILEVERSION@
  PRODUCTVERSION @protobuf_RC_FILEVERSION@
  FILEFLAGSMASK  VS_FFI_FILEFLAGSMASK
  FILEFLAGS      VER_DEBUG
  FILEOS         VOS__WINDOWS32
  FILETYPE       VFT_DLL
BEGIN
    BLOCK "VarFileInfo"
    BEGIN
        // English language (0x409) and the Windows Unicode codepage (1200)
        VALUE "Translation", 0x409, 1200
    END
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "FileDescription", "Compiled with @CMAKE_CXX_COMPILER_ID@ @CMAKE_CXX_COMPILER_VERSION@\0"
            VALUE "ProductVersion", "@protobuf_VERSION@\0"
            VALUE "FileVersion", "@protobuf_VERSION@\0"
            VALUE "InternalName", "protobuf\0"
            VALUE "ProductName", "Protocol Buffers - Google's Data Interchange Format\0"
            VALUE "CompanyName", "Google Inc.\0"
            VALUE "LegalCopyright", "Copyright 2008 Google Inc.  All rights reserved.\0"
            VALUE "Licence", "BSD\0"
            VALUE "Info", "https://developers.google.com/protocol-buffers/\0"
        END
    END
END
