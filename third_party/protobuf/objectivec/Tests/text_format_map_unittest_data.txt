map_int32_int32 {
  key: 100
  value: 1
}
map_int64_int64 {
  key: 101
  value: 1
}
map_uint32_uint32 {
  key: 102
  value: 1
}
map_uint64_uint64 {
  key: 103
  value: 1
}
map_sint32_sint32 {
  key: 104
  value: 1
}
map_sint64_sint64 {
  key: 105
  value: 1
}
map_fixed32_fixed32 {
  key: 106
  value: 1
}
map_fixed64_fixed64 {
  key: 107
  value: 1
}
map_sfixed32_sfixed32 {
  key: 108
  value: 1
}
map_sfixed64_sfixed64 {
  key: 109
  value: 1
}
map_int32_float {
  key: 110
  value: 1
}
map_int32_double {
  key: 111
  value: 1
}
map_bool_bool {
  key: true
  value: false
}
map_string_string {
  key: "112"
  value: "1"
}
map_int32_bytes {
  key: 113
  value: "\001\000\000\000"
}
map_int32_enum {
  key: 114
  value: MAP_ENUM_BAZ
}
map_int32_foreign_message {
  key: 115
  value {
    c: 1
  }
}
