optional_int32: 101
optional_int64: 102
optional_uint32: 103
optional_uint64: 104
optional_sint32: 105
optional_sint64: 106
optional_fixed32: 107
optional_fixed64: 108
optional_sfixed32: 109
optional_sfixed64: 110
optional_float: 111
optional_double: 112
optional_bool: true
optional_string: "115"
optional_bytes: "\001\000\002\003\000\005"
OptionalGroup {
  a: 117
}
optional_nested_message {
  bb: 118
}
optional_foreign_message {
  c: 119
}
optional_import_message {
  d: 120
}
optional_nested_enum: BAZ
optional_foreign_enum: FOREIGN_BAZ
optional_import_enum: IMPORT_BAZ
optional_string_piece: "124"
optional_cord: "125"
repeated_int32: 201
repeated_int32: 301
repeated_int64: 202
repeated_int64: 302
repeated_uint32: 203
repeated_uint32: 303
repeated_uint64: 204
repeated_uint64: 304
repeated_sint32: 205
repeated_sint32: 305
repeated_sint64: 206
repeated_sint64: 306
repeated_fixed32: 207
repeated_fixed32: 307
repeated_fixed64: 208
repeated_fixed64: 308
repeated_sfixed32: 209
repeated_sfixed32: 309
repeated_sfixed64: 210
repeated_sfixed64: 310
repeated_float: 211
repeated_float: 311
repeated_double: 212
repeated_double: 312
repeated_bool: false
repeated_bool: true
repeated_string: "215"
repeated_string: "315"
repeated_bytes: "\330\000\000\000"
repeated_bytes: "<\001\000\000"
RepeatedGroup {
  a: 217
}
RepeatedGroup {
  a: 317
}
repeated_nested_message {
  bb: 218
}
repeated_nested_message {
  bb: 318
}
repeated_foreign_message {
  c: 219
}
repeated_foreign_message {
  c: 319
}
repeated_import_message {
  d: 220
}
repeated_import_message {
  d: 320
}
repeated_nested_enum: BAZ
repeated_nested_enum: BAR
repeated_foreign_enum: FOREIGN_BAZ
repeated_foreign_enum: FOREIGN_BAR
repeated_import_enum: IMPORT_BAZ
repeated_import_enum: IMPORT_BAR
repeated_string_piece: "224"
repeated_string_piece: "324"
repeated_cord: "225"
repeated_cord: "325"
default_int32: 401
default_int64: 402
default_uint32: 403
default_uint64: 404
default_sint32: 405
default_sint64: 406
default_fixed32: 407
default_fixed64: 408
default_sfixed32: 409
default_sfixed64: 410
default_float: 411
default_double: 412
default_bool: false
default_string: "415"
default_bytes: "\240\001\000\000"
default_nested_enum: FOO
default_foreign_enum: FOREIGN_FOO
default_import_enum: IMPORT_FOO
default_string_piece: "424"
default_cord: "425"
