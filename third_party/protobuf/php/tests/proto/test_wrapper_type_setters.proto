syntax = "proto3";

import "google/protobuf/wrappers.proto";

package foo;

message TestWrapperSetters {
  google.protobuf.DoubleValue double_value = 1;
  google.protobuf.FloatValue float_value = 2;
  google.protobuf.Int64Value int64_value = 3;
  google.protobuf.UInt64Value uint64_value = 4;
  google.protobuf.Int32Value int32_value = 5;
  google.protobuf.UInt32Value uint32_value = 6;
  google.protobuf.BoolValue bool_value = 7;
  google.protobuf.StringValue string_value = 8;
  google.protobuf.BytesValue bytes_value = 9;

  oneof wrapped_oneofs {
    google.protobuf.DoubleValue double_value_oneof = 10;
    google.protobuf.StringValue string_value_oneof = 11;
  }

  repeated google.protobuf.StringValue repeated_string_value = 12;

  map<string, google.protobuf.StringValue> map_string_value = 13;
}
