-- automatically generated by the FlatBuffers compiler, do not modify

-- namespace: Sample

local flatbuffers = require('flatbuffers')

local Weapon = {} -- the module
local Weapon_mt = {} -- the class metatable

function Weapon.New()
    local o = {}
    setmetatable(o, {__index = Weapon_mt})
    return o
end
function Weapon.GetRootAsWeapon(buf, offset)
    local n = flatbuffers.N.UOffsetT:Unpack(buf, offset)
    local o = Weapon.New()
    o:Init(buf, n + offset)
    return o
end
function Weapon_mt:Init(buf, pos)
    self.view = flatbuffers.view.New(buf, pos)
end
function Weapon_mt:Name()
    local o = self.view:Offset(4)
    if o ~= 0 then
        return self.view:String(o + self.view.pos)
    end
end
function Weapon_mt:Damage()
    local o = self.view:Offset(6)
    if o ~= 0 then
        return self.view:Get(flatbuffers.N.Int16, o + self.view.pos)
    end
    return 0
end
function Weapon.Start(builder) builder:StartObject(2) end
function Weapon.AddName(builder, name) builder:PrependUOffsetTRelativeSlot(0, name, 0) end
function Weapon.AddDamage(builder, damage) builder:PrependInt16Slot(1, damage, 0) end
function Weapon.End(builder) return builder:EndObject() end

return Weapon -- return the module