name: flatbuffers
base: core18
adopt-info: flatc
summary: FlatBuffers compiler
description: |
  FlatBuffers compiler

  NOTE: This snap also ships the necessary header files required to compile
  projects using flatbuffers, however, for the compilation to work, you have
  to manually add the following path in your project's configuration:

  /snap/flatbuffers/current/include

  If you need to use flatbuffers headers from a location other than the above
  path, it is recommended to not use this snap as that could cause a mismatch.

grade: stable
confinement: strict

parts:
  flatc:
    plugin: cmake
    source: .
    configflags:
      - -GUnix Makefiles
      - -DCMAKE_BUILD_TYPE=Release
    build-packages:
      - g++
      - git
    override-pull: |
      snapcraftctl pull
      tag=$(git describe --tags --abbrev=0)
      count=$(git rev-list $tag.. --count)
      if [ "$count" = 0 ];
      then
        version=$tag
      else
        hash=$(git rev-parse --short HEAD)
        version=$tag+git$count.$hash
      fi
      snapcraftctl set-version $version


apps:
  flatc:
    command: bin/flatc
    plugs:
      - home
      - removable-media
