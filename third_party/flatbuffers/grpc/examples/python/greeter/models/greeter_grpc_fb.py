# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!

import grpc

class Greeter<PERSON>tub(object):
  """ Interface exported by the server. """  
  
  def __init__(self, channel):
    """ Constructor. 
    
    Args: 
    channel: A grpc.Channel. 
    """
    
    self.SayHello = channel.unary_unary(
      "/models.Greeter/Say<PERSON>ello"
      )
    
    self.SayManyHellos = channel.unary_stream(
      "/models.Greeter/SayManyHellos"
      )
    

class GreeterServicer(object):
  """ Interface exported by the server. """  
  
  def SayHello(self, request, context):
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')
  
  
  def SayManyHellos(self, request, context):
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')
  
  

def add_GreeterServicer_to_server(servicer, server):
  rpc_method_handlers = {
    'SayHello': grpc.unary_unary_rpc_method_handler(
      servicer.<PERSON><PERSON><PERSON>
    ),
    'SayManyHellos': grpc.unary_stream_rpc_method_handler(
      servicer.SayManyHellos
    ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
    'models.Greeter', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))

