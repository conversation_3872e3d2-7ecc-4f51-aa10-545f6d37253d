//Generated by gRPC Go plugin
//If you make any local changes, they will be lost
//source: greeter

package models

import (
	context "context"
	flatbuffers "github.com/google/flatbuffers/go"
	grpc "google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// Client API for Greeter service
type GreeterClient interface {
	SayHello(ctx context.Context, in *flatbuffers.Builder,
		opts ...grpc.CallOption) (*HelloReply, error)
	SayManyHellos(ctx context.Context, in *flatbuffers.Builder,
		opts ...grpc.CallOption) (Greeter_SayManyHellosClient, error)
}

type greeterClient struct {
	cc grpc.ClientConnInterface
}

func NewGreeterClient(cc grpc.ClientConnInterface) GreeterClient {
	return &greeterClient{cc}
}

func (c *greeterClient) SayHello(ctx context.Context, in *flatbuffers.Builder,
	opts ...grpc.CallOption) (*HelloReply, error) {
	out := new(HelloReply)
	err := c.cc.Invoke(ctx, "/models.Greeter/SayHello", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greeterClient) SayManyHellos(ctx context.Context, in *flatbuffers.Builder,
	opts ...grpc.CallOption) (Greeter_SayManyHellosClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Greeter_serviceDesc.Streams[0], "/models.Greeter/SayManyHellos", opts...)
	if err != nil {
		return nil, err
	}
	x := &greeterSayManyHellosClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Greeter_SayManyHellosClient interface {
	Recv() (*HelloReply, error)
	grpc.ClientStream
}

type greeterSayManyHellosClient struct {
	grpc.ClientStream
}

func (x *greeterSayManyHellosClient) Recv() (*HelloReply, error) {
	m := new(HelloReply)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// Server API for Greeter service
type GreeterServer interface {
	SayHello(context.Context, *HelloRequest) (*flatbuffers.Builder, error)
	SayManyHellos(*HelloRequest, Greeter_SayManyHellosServer) error
	mustEmbedUnimplementedGreeterServer()
}

type UnimplementedGreeterServer struct {
}

func (UnimplementedGreeterServer) SayHello(context.Context, *HelloRequest) (*flatbuffers.Builder, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SayHello not implemented")
}

func (UnimplementedGreeterServer) SayManyHellos(*HelloRequest, Greeter_SayManyHellosServer) error {
	return status.Errorf(codes.Unimplemented, "method SayManyHellos not implemented")
}

func (UnimplementedGreeterServer) mustEmbedUnimplementedGreeterServer() {}

type UnsafeGreeterServer interface {
	mustEmbedUnimplementedGreeterServer()
}

func RegisterGreeterServer(s grpc.ServiceRegistrar, srv GreeterServer) {
	s.RegisterService(&_Greeter_serviceDesc, srv)
}

func _Greeter_SayHello_Handler(srv interface{}, ctx context.Context,
	dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreeterServer).SayHello(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/models.Greeter/SayHello",
	}

	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreeterServer).SayHello(ctx, req.(*HelloRequest))
	}
	return interceptor(ctx, in, info, handler)
}
func _Greeter_SayManyHellos_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(HelloRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(GreeterServer).SayManyHellos(m, &greeterSayManyHellosServer{stream})
}

type Greeter_SayManyHellosServer interface {
	Send(*flatbuffers.Builder) error
	grpc.ServerStream
}

type greeterSayManyHellosServer struct {
	grpc.ServerStream
}

func (x *greeterSayManyHellosServer) Send(m *flatbuffers.Builder) error {
	return x.ServerStream.SendMsg(m)
}

var _Greeter_serviceDesc = grpc.ServiceDesc{
	ServiceName: "models.Greeter",
	HandlerType: (*GreeterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SayHello",
			Handler:    _Greeter_SayHello_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SayManyHellos",
			Handler:       _Greeter_SayManyHellos_Handler,
			ServerStreams: true,
		},
	},
}
