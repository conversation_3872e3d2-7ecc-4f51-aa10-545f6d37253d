{% set version = "0.1.dev" %}

package:
  name: aten
  version: {{ version }}

source:
  path: ..

build:
  number: 1
  skip: True  # [win]
  script_env:
    - CONDA_CMAKE_ARGS

requirements:
  build:
    - cmake
    - pyyaml
    - setuptools
    - python
    - mkl # [not osx]
  run:
    - mkl # [not osx]

about:
  home: https://github.com/pytorch/pytorch
  license: BSD
  summary: A TENsor library for C++14

extra:
  recipe-maintainers:
    - ezyang
