# This is the PyTorch mypy.ini file (note: don't change this line! -
# test_run_mypy in test/test_type_hints.py uses this string)

[mypy]
plugins = mypy_plugins/check_mypy_version.py

cache_dir = .mypy_cache/normal
warn_unused_configs = True
warn_redundant_casts = True
show_error_codes = True
show_column_numbers = True
check_untyped_defs = True
follow_imports = silent

# do not reenable this:
# https://github.com/pytorch/pytorch/pull/60006#issuecomment-*********
warn_unused_ignores = False

#
# Note: test/ still has syntax errors so can't be added
#
# Typing tests is low priority, but enabling type checking on the
# untyped test functions (using `--check-untyped-defs`) is still
# high-value because it helps test the typing.
#

files =
    torch,
    caffe2,
    test/test_bundled_images.py,
    test/test_bundled_inputs.py,
    test/test_complex.py,
    test/test_datapipe.py,
    test/test_futures.py,
    test/test_numpy_interop.py,
    test/test_torch.py,
    test/test_type_hints.py,
    test/test_type_info.py

#
# `exclude` is a regex, not a list of paths like `files` (sigh)
#
exclude = torch/include/|torch/csrc/|torch/distributed/elastic/agent/server/api.py|torch/testing/_internal|torch/distributed/fsdp/fully_sharded_data_parallel.py

# Minimum version supported - variable annotations were introduced
# in Python 3.8
python_version = 3.8


#
# Extension modules without stubs.
#

[mypy-torch._C._jit_tree_views]
ignore_missing_imports = True

[mypy-torch.for_onnx.onnx]
ignore_missing_imports = True

[mypy-torch.ao.quantization.experimental.apot_utils]
ignore_missing_imports = True

[mypy-torch.ao.quantization.experimental.quantizer]
ignore_missing_imports = True

[mypy-torch.ao.quantization.experimental.observer]
ignore_missing_imports = True

[mypy-torch.ao.quantization.experimental.APoT_tensor]
ignore_missing_imports = True

[mypy-torch.ao.quantization.experimental.fake_quantize_function]
ignore_missing_imports = True

[mypy-torch.ao.quantization.experimental.fake_quantize]
ignore_missing_imports = True

#
# Files with various errors. Mostly real errors, possibly some false
# positives as well.
#

[mypy-test_torch]
check_untyped_defs = False

# Excluded from mypy due to OpInfos being annoying to type
[mypy-torch.testing._internal.common_methods_invocations.*]
ignore_errors = True

[mypy-torch.testing._internal.hypothesis_utils.*]
ignore_errors = True

[mypy-torch.testing._internal.common_quantization.*]
ignore_errors = True

[mypy-torch.testing._internal.generated.*]
ignore_errors = True

[mypy-torch.testing._internal.distributed.*]
ignore_errors = True

[mypy-torch.nn.modules.pooling]
ignore_errors = True

[mypy-torch.nn.parallel._functions]
ignore_errors = True

[mypy-torch._appdirs]
ignore_errors = True

[mypy-torch.multiprocessing.pool]
ignore_errors = True

[mypy-torch.overrides]
ignore_errors = True

#
# Files with 'type: ignore' comments that are needed if checked with mypy-strict.ini
#

[mypy-tools.render_junit]
warn_unused_ignores = False

[mypy-tools.generate_torch_version]
warn_unused_ignores = False

#
# Adding type annotations to caffe2 is probably not worth the effort
# only work on this if you have a specific reason for it, otherwise
# leave these ignores as they are.
#

[mypy-caffe2.python.*]
ignore_errors = True

[mypy-caffe2.proto.*]
ignore_errors = True

[mypy-caffe2.core.nomnigraph.op_gen]
ignore_errors = True

[mypy-caffe2.contrib.playground.*]
ignore_errors = True

[mypy-caffe2.contrib.gloo.gloo_test]
ignore_errors = True

[mypy-caffe2.contrib.warpctc.ctc_ops_test]
ignore_errors = True

[mypy-caffe2.contrib.prof.cuda_profile_ops_test]
ignore_errors = True

[mypy-caffe2.contrib.nccl.nccl_ops_test]
ignore_errors = True

[mypy-caffe2.distributed.store_ops_test_util]
ignore_errors = True

[mypy-caffe2.experiments.python.device_reduce_sum_bench]
ignore_errors = True

[mypy-caffe2.experiments.python.SparseTransformer]
ignore_errors = True

[mypy-caffe2.experiments.python.convnet_benchmarks]
ignore_errors = True

[mypy-caffe2.contrib.aten.aten_test]
ignore_errors = True

[mypy-caffe2.contrib.aten.docs.sample]
ignore_errors = True

[mypy-caffe2.contrib.tensorboard.tensorboard_exporter]
ignore_errors = True

[mypy-caffe2.contrib.tensorboard.tensorboard_exporter_test]
ignore_errors = True

[mypy-caffe2.quantization.server.*]
ignore_errors = True

#
# Third party dependencies that don't have types.
#

[mypy-tensorflow.*]
ignore_missing_imports = True

[mypy-tensorboard.*]
ignore_missing_imports = True

[mypy-onnx.*]
ignore_missing_imports = True

[mypy-matplotlib.*]
ignore_missing_imports = True

[mypy-numpy.*]
ignore_missing_imports = True

[mypy-hypothesis.*]
ignore_missing_imports = True

[mypy-tqdm.*]
ignore_missing_imports = True

[mypy-multiprocessing.*]
ignore_missing_imports = True

[mypy-setuptools.*]
ignore_missing_imports = True

[mypy-distutils.*]
ignore_missing_imports = True

[mypy-nvd3.*]
ignore_missing_imports = True

[mypy-future.utils]
ignore_missing_imports = True

[mypy-past.builtins]
ignore_missing_imports = True

[mypy-numba.*]
ignore_missing_imports = True

[mypy-PIL.*]
ignore_missing_imports = True

[mypy-moviepy.*]
ignore_missing_imports = True

[mypy-cv2.*]
ignore_missing_imports = True

[mypy-torchvision.*]
ignore_missing_imports = True

[mypy-pycuda.*]
ignore_missing_imports = True

[mypy-tensorrt.*]
ignore_missing_imports = True

[mypy-tornado.*]
ignore_missing_imports = True

[mypy-pydot.*]
ignore_missing_imports = True

[mypy-networkx.*]
ignore_missing_imports = True

[mypy-scipy.*]
ignore_missing_imports = True

[mypy-IPython.*]
ignore_missing_imports = True

[mypy-google.protobuf.textformat]
ignore_missing_imports = True

[mypy-lmdb.*]
ignore_missing_imports = True

[mypy-mpi4py.*]
ignore_missing_imports = True

[mypy-skimage.*]
ignore_missing_imports = True

[mypy-librosa.*]
ignore_missing_imports = True

[mypy-mypy.*]
ignore_missing_imports = True

[mypy-xml.*]
ignore_missing_imports = True

[mypy-boto3.*]
ignore_missing_imports = True

[mypy-dill.*]
ignore_missing_imports = True

[mypy-usort.*]
ignore_missing_imports = True
