# This is the PyTorch mypy-strict.ini file (note: don't change this line! -
# test_run_mypy in test/test_type_hints.py uses this string)

# Unlike mypy.ini, it enforces very strict typing rules. The intention is for
# this config file to be used to ENFORCE that people are using mypy on codegen
# files.

[mypy]
python_version = 3.8
plugins = mypy_plugins/check_mypy_version.py

cache_dir = .mypy_cache/strict
strict_optional = True
show_error_codes = True
show_column_numbers = True
warn_no_return = True
disallow_any_unimported = True

# Across versions of mypy, the flags toggled by --strict vary.  To ensure
# we have reproducible type check, we instead manually specify the flags
warn_unused_configs = True
disallow_any_generics = True
disallow_subclassing_any = True
disallow_untyped_calls = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True
warn_redundant_casts = True
warn_return_any = True
implicit_reexport = False
strict_equality = True

# do not reenable this:
# https://github.com/pytorch/pytorch/pull/60006#issuecomment-866130657
warn_unused_ignores = False

files =
    .github,
    benchmarks/instruction_counts,
    tools,
    torch/profiler/_memory_profiler.py,
    torch/utils/_pytree.py,
    torch/utils/benchmark/utils/common.py,
    torch/utils/benchmark/utils/timer.py,
    torch/utils/benchmark/utils/valgrind_wrapper

# Specifically enable imports of benchmark utils. As more of `torch` becomes
# strict compliant, those modules can be enabled as well.
[mypy-torch.utils.benchmark.utils.*]
follow_imports = normal

# Don't follow imports as much of `torch` is not strict compliant.
[mypy-torch]
follow_imports = skip

[mypy-torch.*]
follow_imports = skip

# Missing stubs.

[mypy-numpy]
ignore_missing_imports = True

[mypy-mypy.*]
ignore_missing_imports = True

[mypy-usort.*]
ignore_missing_imports = True
