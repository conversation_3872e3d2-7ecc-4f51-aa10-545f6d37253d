apply plugin: 'com.android.application'

repositories {
    jcenter()
    maven {
        url "https://oss.sonatype.org/content/repositories/snapshots"
    }
    flatDir {
        dirs 'aars'
    }
}

android {
    configurations {
        extractForNativeBuild
    }
    compileOptions {
        sourceCompatibility 1.8
        targetCompatibility 1.8
    }
    compileSdkVersion rootProject.compileSdkVersion
    buildToolsVersion rootProject.buildToolsVersion
    defaultConfig {
        applicationId "org.pytorch.testapp"
        minSdkVersion rootProject.minSdkVersion
        targetSdkVersion rootProject.targetSdkVersion
        versionCode 1
        versionName "1.0"
        ndk {
            abiFilters ABI_FILTERS.split(",")
        }
        // Commented due to dependency on local copy of pytorch_android aar to aars folder
        //externalNativeBuild {
        //    cmake {
        //        abiFilters ABI_FILTERS.split(",")
        //        arguments "-DANDROID_STL=c++_shared"
        //    }
        //}
        buildConfigField("String", "MODULE_ASSET_NAME", "\"mobilenet2q.pt\"")
        buildConfigField("String", "LOGCAT_TAG", "@string/app_name")
        buildConfigField("long[]", "INPUT_TENSOR_SHAPE", "new long[]{1, 3, 224, 224}")
        buildConfigField("boolean", "NATIVE_BUILD", 'false')
        buildConfigField("boolean", "USE_VULKAN_DEVICE", 'false')
        addManifestPlaceholders([APP_NAME: "@string/app_name", MAIN_ACTIVITY: "org.pytorch.testapp.MainActivity"])
    }
    buildTypes {
        debug {
            minifyEnabled false
            debuggable true
        }
        release {
            minifyEnabled false
        }
    }
    // Commented due to dependency on local copy of pytorch_android aar to aars folder
    //externalNativeBuild {
    //    cmake {
    //        path "CMakeLists.txt"
    //    }
    //}
    flavorDimensions "model", "build", "activity"
    productFlavors {
        mnet {
            dimension "model"
            applicationIdSuffix ".mnet"
            buildConfigField("String", "MODULE_ASSET_NAME", "\"mnet.pt\"")
            addManifestPlaceholders([APP_NAME: "MNET"])
            buildConfigField("String", "LOGCAT_TAG", "\"pytorch-mnet\"")
        }
        mnetVulkan {
            dimension "model"
            applicationIdSuffix ".mnet_vulkan"
            buildConfigField("String", "MODULE_ASSET_NAME", "\"mnet_vulkan.pt\"")
            buildConfigField("boolean", "USE_VULKAN_DEVICE", 'true')
            addManifestPlaceholders([APP_NAME: "MNET_VULKAN"])
            buildConfigField("String", "LOGCAT_TAG", "\"pytorch-mnet-vulkan\"")
        }
        resnet18 {
            dimension "model"
            applicationIdSuffix ".resnet18"
            buildConfigField("String", "MODULE_ASSET_NAME", "\"resnet18.pt\"")
            addManifestPlaceholders([APP_NAME: "RN18"])
            buildConfigField("String", "LOGCAT_TAG", "\"pytorch-resnet18\"")
        }
        local {
            dimension "build"
        }
        nightly {
            dimension "build"
        }
        aar {
            dimension "build"
        }
        // Commented due to dependency on local copy of pytorch_android aar to aars folder
        //nativeBuild {
        //    dimension "build"
        //    buildConfigField("boolean", "NATIVE_BUILD", "true")
        //}
        camera {
            dimension "activity"
            addManifestPlaceholders([MAIN_ACTIVITY: "org.pytorch.testapp.CameraActivity"])
        }
        base {
            dimension "activity"
            sourceSets {
                main {
                    java {
                        exclude 'org/pytorch/testapp/CameraActivity.java'
                    }
                }
            }
        }
    }
    packagingOptions {
        doNotStrip '**.so'
    }

    // Filtering for CI
    if (!testAppAllVariantsEnabled.toBoolean()) {
        variantFilter { variant ->
            def names = variant.flavors*.name
            if (names.contains("nightly")
                || names.contains("camera")
                || names.contains("aar")
                || names.contains("nativeBuild")) {
                setIgnore(true)
            }
        }
    }
}

tasks.all { task ->
    // Disable externalNativeBuild for all but nativeBuild variant
    if (task.name.startsWith('externalNativeBuild')
          && !task.name.contains('NativeBuild')) {
        task.enabled = false
    }
}

dependencies {
    implementation 'com.android.support:appcompat-v7:28.0.0'
    implementation 'com.facebook.soloader:nativeloader:0.10.4'

    localImplementation project(':pytorch_android')
    localImplementation project(':pytorch_android_torchvision')

    // Commented due to dependency on local copy of pytorch_android aar to aars folder
    //nativeBuildImplementation(name: 'pytorch_android-release', ext: 'aar')
    //nativeBuildImplementation(name: 'pytorch_android_torchvision-release', ext: 'aar')
    //extractForNativeBuild(name: 'pytorch_android-release', ext: 'aar')

    nightlyImplementation 'org.pytorch:pytorch_android:1.12.0-SNAPSHOT'
    nightlyImplementation 'org.pytorch:pytorch_android_torchvision:1.12.0-SNAPSHOT'

    aarImplementation(name:'pytorch_android', ext:'aar')
    aarImplementation(name:'pytorch_android_torchvision', ext:'aar')
    aarImplementation 'com.facebook.soloader:nativeloader:0.10.4'
    aarImplementation 'com.facebook.fbjni:fbjni-java-only:0.2.2'

    def camerax_version = "1.0.0-alpha05"
    cameraImplementation "androidx.camera:camera-core:$camerax_version"
    cameraImplementation "androidx.camera:camera-camera2:$camerax_version"
    cameraImplementation 'com.google.android.material:material:1.0.0-beta01'
}

task extractAARForNativeBuild {
    doLast {
        configurations.extractForNativeBuild.files.each {
            def file = it.absoluteFile
            copy {
                from zipTree(file)
                into "$buildDir/$file.name"
                include "headers/**"
                include "jni/**"
            }
        }
    }
}

tasks.whenTaskAdded { task ->
  if (task.name.contains('externalNativeBuild')) {
    task.dependsOn(extractAARForNativeBuild)
  }
}
