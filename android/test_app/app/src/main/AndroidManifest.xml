<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="org.pytorch.testapp">

    <application
        android:allowBackup="true"
        android:label="${APP_NAME}"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">

        <activity android:name="${MAIN_ACTIVITY}">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>

    <uses-permission android:name="android.permission.CAMERA" />

    <!--
     Permissions required by the Snapdragon Profiler to collect GPU metrics.
    -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
</manifest>
