// Copyright (c) Facebook, Inc. and its affiliates.
//
// This source code is licensed under the Apache-2 license found in the
// LICENSE file in the root directory of this source tree.

plugins {
    id 'java-library'
}

repositories {
    mavenLocal()
    jcenter()
}

sourceSets {
    main {
        java {
            srcDir '../src/main/java'
            exclude 'org/pytorch/PyTorchAndroid.java'
            exclude 'org/pytorch/LiteModuleLoader.java'
            exclude 'org/pytorch/LiteNativePeer.java'
        }
    }
    test {
        java {
            srcDir '../src/androidTest/java'
            exclude '**/PytorchInstrumented*'
            exclude '**/PytorchLiteInstrumented*'
        }
        resources.srcDirs = ["../src/androidTest/assets"]
    }
}

dependencies {
    compileOnly 'com.google.code.findbugs:jsr305:3.0.1'
    implementation 'com.facebook.soloader:nativeloader:0.10.1'
    implementation 'com.facebook.fbjni:fbjni-java-only:0.2.2'
    testImplementation 'junit:junit:4.12'
}

apply from: rootProject.file('gradle/release.gradle')
