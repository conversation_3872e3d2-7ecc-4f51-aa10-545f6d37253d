name: Android CI

on: [push, pull_request]

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v1
    - name: set up JDK 1.8
      uses: actions/setup-java@v1
      with:
        java-version: 1.8
    - name: Build with Gradle
      run: |
        source scripts/android-setup.sh && installAndroidSDK
        ./gradlew assembleDebug
    - name: Run tests
      run: |
        source scripts/android-setup.sh && installAndroidSDK && installsdk 'cmake;3.10.2.4988404'
        scripts/run-host-tests.sh
