registration JavaClass definition and method registration
constructor Wrapping constructors
basic_methods Basic method usage (Java to C++ and C++ to Java)
primitives Methods using Java primitives
strings Methods using Java Strings
primitive_arrays Working with arrays of primitives
class_arrays Working with arrays of objects
references References
nested_class Defining a nested class
inheritance Defining a child class
fields Getting and setting fields
jobject_jclass Working with JObject and JClass
simple_exceptions Catching and throwing exceptions
boxed Working with boxed primitives
iterables Working with Collections
byte_buffer Transferring data with direct ByteBuffer
