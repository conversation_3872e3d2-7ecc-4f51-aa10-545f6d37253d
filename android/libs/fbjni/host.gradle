/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

plugins {
    id 'java-library'
    id 'com.vanniktech.maven.publish' version '0.13.0' apply false
}

repositories {
    mavenLocal()
    jcenter()
}

sourceSets {
    main {
        java.srcDir 'java'
    }
    test {
        java.srcDir 'test'
    }
}

dependencies {
    compileOnly 'com.google.code.findbugs:jsr305:3.0.2'
    implementation 'com.facebook.soloader:nativeloader:0.10.1'
    testImplementation 'junit:junit:4.12'
    testImplementation 'org.easytesting:fest-assert-core:2.0M10'
    testImplementation 'org.mockito:mockito-core:2.28.2'
}

// Overriding the artifacts name globally defined in gradle.properties
POM_PACKAGING = 'jar'
POM_ARTIFACT_ID = 'fbjni-java-only'

apply from: rootProject.file('gradle/release.gradle')
