/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include <fbjni/detail/Log.h>

#define EXPECT(X)                         \
  do {                                    \
    if (!(X)) {                           \
      FBJNI_LOGE("[%s:%d] Expectation failed: %s", __FILE__, __LINE__,  #X);  \
      return JNI_FALSE;                   \
    }                                     \
  } while (false)

