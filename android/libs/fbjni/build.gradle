/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

buildscript {
    repositories {
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:4.1.2'
    }
}

plugins {
    id 'com.vanniktech.maven.publish' version '0.13.0' apply false
}

apply plugin: 'com.android.library'
apply plugin: 'maven'

repositories {
    google()
    jcenter()
}

android {
    compileSdkVersion 29
    buildToolsVersion '29.0.3'
    ndkVersion '21.1.6352462'

    externalNativeBuild {
        cmake {
            path 'CMakeLists.txt'
        }
    }

    defaultConfig {
        minSdkVersion 15
        targetSdkVersion 29
        buildConfigField "boolean", "IS_INTERNAL_BUILD", 'true'

        sourceSets {
            main {
                manifest.srcFile 'manifest/AndroidManifest.xml'
                java.srcDir 'java'
            }
        }

        ndk {
            abiFilters 'x86', 'armeabi-v7a', 'x86_64', 'arm64-v8a'
        }

        externalNativeBuild {
            cmake {
                arguments '-DANDROID_TOOLCHAIN=clang', '-DANDROID_STL=c++_shared'
                targets 'fbjni'
                version '3.10.2.4988404'
            }
        }

        buildFeatures {
            prefabPublishing true
        }

        prefab {
            fbjni {
                headers 'cxx/'
            }
        }
    }
}


dependencies {
    compileOnly 'com.google.code.findbugs:jsr305:3.0.2'
    implementation 'com.facebook.soloader:nativeloader:0.10.1'
}

apply from: rootProject.file('gradle/release.gradle')
