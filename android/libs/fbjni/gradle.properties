# Copyright (c) Facebook, Inc. and its affiliates.
#
# This source code is licensed under the Apache-2 license found in the LICENSE file
# in the root directory of this source tree.

# POM publishing constants
VERSION_NAME=0.2.2
GROUP=com.facebook.fbjni
SONATYPE_STAGING_PROFILE=comfacebook
POM_URL=https://github.com/facebookincubator/fbjni
POM_SCM_URL=https://github.com/facebookincubator/fbjni.git
POM_SCM_CONNECTION=scm:git:https://github.com/facebookincubator/fbjni.git
POM_SCM_DEV_CONNECTION=scm:git:**************:facebookincubator/fbjni.git
POM_LICENSE_NAME=APACHE-2
POM_LICENSE_URL=https://github.com/facebookincubator/fbjni/blob/master/LICENSE
POM_LICENSE_DIST=repo
POM_DEVELOPER_ID=facebook
POM_DEVELOPER_NAME=facebook
POM_ISSUES_URL=https://github.com/facebookincubator/fbjni/issues/

# Gradle internals
org.gradle.internal.repository.max.retries=10
org.gradle.internal.repository.initial.backoff=1250

android.useAndroidX=true
android.enableJetifier=true

# Local artifact settings
POM_NAME=fbjni
POM_DESCRIPTION=Facebook JNI helpers library
POM_ARTIFACT_ID=fbjni
POM_PACKAGING=aar
