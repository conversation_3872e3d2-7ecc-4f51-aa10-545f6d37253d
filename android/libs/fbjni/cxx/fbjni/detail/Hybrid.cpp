/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <fbjni/fbjni.h>

namespace facebook {
namespace jni {

namespace detail {

local_ref<HybridData> HybridData::create() {
  return newInstance();
}

}

namespace {
void deleteNative(alias_ref<jclass>, jlong ptr) {
  delete reinterpret_cast<detail::BaseHybridClass*>(ptr);
}
}

void HybridDataOnLoad() {
  registerNatives("com/facebook/jni/HybridData$Destructor", {
      makeNativeMethod("deleteNative", deleteNative),
  });
}

}}
