# 2020-11-12 Enabled ShellCheck on `.jenkins/pytorch`
65d5004b09fd8d5deac173a3aaa259f46eaa0d67
# 2021-01-20 Replaced `   ` with `...` in many doctests
c147aa306c6386a753fdff24b48d04e803070a63
# 2021-03-05 Removed all trailing whitespace
8c798e062216278673a75bac0848ea69a8bd3f03
# 2021-03-30 Normalized trailing newlines
5bcbbf537327f6e8328289c25a3a453a2444d984
# 2021-03-31 Autogenerated Markdown ToCs
a74b10def961ab090385f291ee06e66db99c1a2f
# 2021-04-02 Enabled more ShellCheck warnings
09670c7d43b9abce862a6bf71d8cc89e64764bdb
# 2021-04-08 Removed all non-breaking spaces
cc11aaaa60aadf28e3ec278bce26a42c1cd68a4f
# 2021-04-13 Expanded many wildcard imports
4753100a3baa96273204c361c8452afb7b59836f
# 2021-04-19 Removed all unqualified `noqa`
e3900d2ba5c9f91a24a9ce34520794c8366d5c54
# 2021-04-21 Removed all unqualified `type: ignore`
75024e228ca441290b6a1c2e564300ad507d7af6
# 2021-04-30 [PyTorch] Autoformat c10
44cc873fba5e5ffc4d4d4eef3bd370b653ce1ce1
# 2021-05-14 Removed all versionless Python shebangs
2e26976ad3b06ce95dd6afccfdbe124802edf28f
# 2021-06-07 Strictly typed everything in `.github` and `tools`
737d920b21db9b4292d056ee1329945990656304
# 2022-06-09 Apply clang-format to ATen headers
95b15c266baaf989ef7b6bbd7c23a2d90bacf687
# 2022-06-11 [lint] autoformat test/cpp and torch/csrc
30fb2c4abaaaa966999eab11674f25b18460e609
