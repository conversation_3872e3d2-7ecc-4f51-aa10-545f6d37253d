cff-version: 1.2.0
message: If you use this software, please cite it as below.
title: PyTorch
authors:
  - family-names: PyTorch Team
url: https://pytorch.org
preferred-citation:
  type: conference-paper
  title: "PyTorch: An Imperative Style, High-Performance Deep Learning Library"
  authors:
    - family-names: <PERSON><PERSON>ke
      given-names: Adam
    - family-names: Gross
      given-names: Sam
    - family-names: <PERSON><PERSON>
      given-names: Francisco
    - family-names: <PERSON><PERSON>
      given-names: Adam
    - family-names: <PERSON><PERSON>
      given-names: <PERSON>
    - family-names: <PERSON>an
      given-names: Gregory
    - family-names: Killeen
      given-names: Trevor
    - family-names: <PERSON>
      given-names: <PERSON>eming
    - family-names: G<PERSON>lshein
      given-names: Natalia
    - family-names: Antiga
      given-names: Luca
    - family-names: Des<PERSON>ison
      given-names: Alban
    - family-names: <PERSON><PERSON>
      given-names: Andreas
    - family-names: Yang
      given-names: Edward
    - family-names: De<PERSON><PERSON>
      given-names: Zachary
    - family-names: <PERSON><PERSON>
      given-names: Martin
    - family-names: <PERSON><PERSON>i
      given-names: <PERSON><PERSON><PERSON>
    - family-names: <PERSON><PERSON><PERSON><PERSON><PERSON>
      given-names: <PERSON>sank
    - family-names: Steiner
      given-names: <PERSON><PERSON>
    - family-names: <PERSON>
      given-names: Lu
    - family-names: Bai
      given-names: <PERSON><PERSON>e
    - family-names: <PERSON><PERSON><PERSON>
      given-names: Soumith
  collection-title: Advances in Neural Information Processing Systems 32
  collection-type: proceedings
  editors:
    - family-names: Wallach
      given-names: H.
    - family-names: Larochelle
      given-names: H.
    - family-names: <PERSON>gelzimer
      given-names: A.
    - family-names: "d'Alch<PERSON>-Buc"
      given-names: F.
    - family-names: Fox
      given-names: E.
    - family-names: Garnett
      given-names: R.
  start: 8024
  end: 8035
  year: 2019
  publisher:
    name: Curran Associates, Inc.
  url: http://papers.neurips.cc/paper/9015-pytorch-an-imperative-style-high-performance-deep-learning-library.pdf
